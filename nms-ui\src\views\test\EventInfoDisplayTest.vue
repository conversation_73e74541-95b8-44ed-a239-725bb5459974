<template>
  <div class="test-page">
    <h1>EventInfoDisplay 组件测试页面</h1>
    <p>测试动态字段显示逻辑和过滤开关功能</p>
    
    <EventInfoDisplay 
      event-id="EVT2025070200000178"
      :loading="false"
      :error="null"
    />
  </div>
</template>

<script setup lang="ts">
import EventInfoDisplay from '@/views/evt/analyze/components/EventInfoDisplay.vue'
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #1890ff;
  margin-bottom: 10px;
}

p {
  color: #666;
  margin-bottom: 20px;
}
</style>
