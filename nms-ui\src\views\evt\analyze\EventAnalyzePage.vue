<!--
  事件分析管理页面 - 渐进式增强版本

  <AUTHOR>
  @version 1.5.0
  @since 2025-02-14
-->

<template>
  <div class="event-analyze-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>事件分析详情</h1>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="handleOpenAnalysisTools">
            <template #icon><PlusOutlined /></template>
            分析工具
          </a-button>
          <a-button @click="handleRefresh" :loading="isRefreshing">
            <ReloadOutlined />
            刷新
          </a-button>
          <a-button @click="handleBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isInitializing" class="loading-container">
      <a-spin size="large" tip="正在加载事件分析数据...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="main-content">
      <!-- 中间面板 - 分析工作区 -->
      <div class="center-panel">
        <AnalysisWorkspace
          :initial-data="analysisData"
          :readonly="isViewMode"
          :loading="isLoadingAnalysis"
          :event-id="eventId"
          @data-change="handleAnalysisDataChange"
          @save="handleSave"
          @submit="handleSubmit"
        />
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 知识库 -->
        <a-card title="知识库" class="knowledge-card" :loading="isLoadingKnowledge">
          <div class="knowledge-content">
            <a-input-search
              placeholder="搜索相关知识..."
              style="margin-bottom: 12px;"
            />
            <a-list size="small">
              <a-list-item>
                <a-list-item-meta
                  title="跌倒事件分析指南"
                  description="患者跌倒事件的标准分析流程"
                />
              </a-list-item>
              <a-list-item>
                <a-list-item-meta
                  title="根因分析方法"
                  description="5Why分析法的应用指导"
                />
              </a-list-item>
              <a-list-item>
                <a-list-item-meta
                  title="改进措施模板"
                  description="常用的改进措施参考模板"
                />
              </a-list-item>
            </a-list>
          </div>
        </a-card>

        <!-- 协作讨论 -->
        <a-card title="协作讨论" class="collaboration-card">
          <div class="collaboration-content">
            <div class="message-list">
              <div class="message-item">
                <div class="message-header">
                  <span class="sender">张医生</span>
                  <span class="time">10分钟前</span>
                </div>
                <div class="message-content">
                  这个事件需要重点关注环境因素
                </div>
              </div>
            </div>
            <a-input-search
              placeholder="输入讨论内容..."
              enter-button="发送"
              style="margin-top: 12px;"
            />
          </div>
        </a-card>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  SaveOutlined,
  CheckOutlined
} from '@ant-design/icons-vue'
import { ensureDayjsObject, formatDateTime } from '@/utils/dateUtils'

// 导入组件
import AnalysisWorkspace from './components/AnalysisWorkspace.vue'

// 导入API
import { getAnalysisDetails } from '@/api/evt/analyze'

// 路由和基础状态
const route = useRoute()
const router = useRouter()

// 页面状态
const isInitializing = ref(true)
const isRefreshing = ref(false)
const isLoadingAnalysis = ref(false)
const isLoadingKnowledge = ref(false)
const isSaving = ref(false)
const isSubmitting = ref(false)
const isViewMode = ref(false)

// 数据状态
const eventId = computed(() => route.params.id as string)
const taskId = computed(() => route.query.taskId as string)



const analysisData = reactive({
  analysisData: {
    meetingInfo: {},
    causes: [],
    measures: [],
    conclusion: {}
  },
  toolsData: {
    fishbone: {
      problem: '',
      mainBones: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    },
    fiveWhy: {
      problem: '',
      whyLevels: [],
      conclusion: '',
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    },
    pdca: {
      cycleName: '',
      plan: [],
      do: [],
      check: [],
      act: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    },
    fmea: {
      processName: '',
      riskItems: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    }
  }
})



// 事件处理函数
async function handleRefresh() {
  isRefreshing.value = true
  try {
    await loadAnalysisData()
    message.success('刷新成功')
  } catch (error) {
    message.error('刷新失败')
  } finally {
    isRefreshing.value = false
  }
}

function handleBack() {
  router.push('/evt/analyze')
}

// 处理打开分析工具
function handleOpenAnalysisTools() {
  // 在新标签页中打开分析工具页面
  const toolsUrl = router.resolve('/evt/analyze/tools').href
  window.open(toolsUrl, '_blank')
}



// 分析数据变化处理
function handleAnalysisDataChange(data: any) {
  Object.assign(analysisData, data)
  console.log('分析数据已更新:', data)
}

// 分析数据相关处理
async function handleSave() {
  isSaving.value = true
  try {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('分析数据已保存')
  } catch (error) {
    message.error('保存失败')
  } finally {
    isSaving.value = false
  }
}

async function handleSubmit() {
  isSubmitting.value = true
  try {
    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 1500))
    message.success('分析已提交')
  } catch (error) {
    message.error('提交失败')
  } finally {
    isSubmitting.value = false
  }
}



async function loadAnalysisData() {
  isLoadingAnalysis.value = true
  try {
    console.log('🔍 EventAnalyzePage: 开始加载分析数据', {
      eventId: eventId.value,
      taskId: taskId.value
    })

    // 获取真实的分析数据
    let analysisResponse = null

    try {
      // 使用taskId来获取分析数据，如果没有taskId则使用eventId
      const analysisId = taskId.value || eventId.value
      analysisResponse = await getAnalysisDetails(analysisId)
      console.log('✅ EventAnalyzePage: 获取到已存在的分析数据:', analysisResponse)
    } catch (error) {
      console.log('ℹ️ EventAnalyzePage: 未找到已存在的分析数据，将创建新的分析记录')
    }

    // 构建分析数据结构
    const analysisData_new = {
      meetingInfo: {
        meetingTime: analysisResponse?.meetingTime ? ensureDayjsObject(analysisResponse.meetingTime) : null,
        location: analysisResponse?.meetingLocation || '',
        moderator: analysisResponse?.moderator || '',
        participants: analysisResponse?.participants || ''
      },
      causes: analysisResponse?.causes || [],
      measures: analysisResponse?.measures || [],
      conclusion: {
        summary: analysisResponse?.conclusion || '',
        recommendations: analysisResponse?.recommendations || '',
        expectedOutcome: analysisResponse?.expectedOutcome || ''
      }
    }

    // 更新分析数据
    Object.assign(analysisData.analysisData, analysisData_new)

    // 初始化工具数据
    if (!analysisResponse) {
      // 使用事件ID初始化工具数据
      analysisData.toolsData.fishbone.problem = `事件 ${eventId.value} 分析`
      analysisData.toolsData.fiveWhy.problem = `事件 ${eventId.value} 分析`
      analysisData.toolsData.pdca.cycleName = `事件 ${eventId.value} - PDCA分析`
      analysisData.toolsData.fmea.processName = `事件 ${eventId.value} - FMEA分析`
    } else {
      // 使用已存在的分析工具数据
      if (analysisResponse.analysisTools) {
        analysisResponse.analysisTools.forEach((tool: any) => {
          if (tool.toolType === 'fishbone' && tool.toolData) {
            Object.assign(analysisData.toolsData.fishbone, tool.toolData)
          } else if (tool.toolType === 'fiveWhy' && tool.toolData) {
            Object.assign(analysisData.toolsData.fiveWhy, tool.toolData)
          } else if (tool.toolType === 'pdca' && tool.toolData) {
            Object.assign(analysisData.toolsData.pdca, tool.toolData)
          } else if (tool.toolType === 'fmea' && tool.toolData) {
            Object.assign(analysisData.toolsData.fmea, tool.toolData)
          }
        })
      }
    }

    console.log('✅ EventAnalyzePage: 分析数据加载完成', {
      hasExistingData: !!analysisResponse,
      meetingInfo: analysisData.analysisData.meetingInfo,
      causesCount: analysisData.analysisData.causes.length,
      measuresCount: analysisData.analysisData.measures.length
    })

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '加载分析数据失败'
    console.error('❌ EventAnalyzePage: 加载分析数据失败', error)
    message.error(`加载分析数据失败: ${errorMsg}`)
  } finally {
    isLoadingAnalysis.value = false
  }
}

// 页面初始化
onMounted(async () => {
  try {
    isInitializing.value = true

    // 检查页面模式
    isViewMode.value = route.query.mode === 'view'

    // 加载分析数据
    try {
      await loadAnalysisData()
    } catch (error) {
      console.error('❌ EventAnalyzePage: 分析数据加载失败', error)
    }

  } catch (error) {
    console.error('❌ EventAnalyzePage: 页面初始化失败', error)
    message.error('页面初始化失败，请尝试刷新页面')
  } finally {
    isInitializing.value = false
  }
})
</script>

<style scoped lang="less">
.event-analyze-page {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;

    h1 {
      margin: 0;
      color: #1890ff;
      font-size: 24px;
    }
  }

  .loading-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-placeholder {
      width: 100%;
      height: 200px;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;

    .center-panel {
      flex: 1;
      min-width: 0;
      overflow: hidden;
    }

    .right-panel {
      width: 320px;
      flex-shrink: 0;
      overflow-y: auto;
    }
  }



  // 知识库和协作讨论卡片样式
  .knowledge-card,
  .collaboration-card {
    margin-bottom: 16px;

    .knowledge-content,
    .collaboration-content {
      .message-list {
        max-height: 200px;
        overflow-y: auto;

        .message-item {
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;

            .sender {
              font-weight: 500;
              color: #1890ff;
            }

            .time {
              font-size: 12px;
              color: #999;
            }
          }

          .message-content {
            color: #333;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .event-analyze-page {
    .main-content {
      .right-panel {
        width: 280px;
      }
    }
  }
}

@media (max-width: 992px) {
  .event-analyze-page {
    .main-content {
      flex-direction: column;
      gap: 16px;

      .center-panel {
        order: 1;
        min-height: 500px;
      }

      .right-panel {
        order: 2;
        width: 100%;
        max-height: 400px;
      }
    }
  }
}

@media (max-width: 768px) {
  .event-analyze-page {
    padding: 12px;

    .page-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      h1 {
        font-size: 20px;
      }
    }

    .main-content {
      gap: 12px;
    }
  }
}
</style>
