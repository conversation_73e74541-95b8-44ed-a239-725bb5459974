<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventInfoDisplay 组件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .event-info-display {
            height: 600px;
            display: flex;
            flex-direction: column;
            background: #f5f5f5;
        }
        .loading-container,
        .error-container,
        .empty-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 400px;
        }
        .loading-text {
            margin-top: 16px;
            color: #666;
        }
        .event-info-content {
            flex: 1;
            overflow: hidden;
        }
        .scrollable-content {
            height: 100%;
            overflow-y: auto;
            padding: 16px;
        }
        .info-card {
            margin-bottom: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e8f4fd;
        }
        .card-title-with-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .dynamic-fields-section .dynamic-fields-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
        }
        .dynamic-field-item {
            padding: 12px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: #ffffff;
        }
        .dynamic-field-item.filled-field {
            border-color: #52c41a;
            background: #f6ffed;
        }
        .dynamic-field-item.empty-field {
            border-color: #d9d9d9;
            background: #fafafa;
        }
        .field-label {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .field-value .text-field-value {
            color: #666;
        }
        .field-value .text-field-value.empty-value {
            color: #999;
            font-style: italic;
        }
        .empty-indicator {
            color: #999;
            font-size: 12px;
        }
        .switch-container {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        .switch-container.checked {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .tag.blue { background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff; }
        .tag.green { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .tag.orange { background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591; }
        .tag.red { background: #fff2f0; color: #ff4d4f; border: 1px solid #ffb3b3; }
        .divider {
            margin: 16px 0;
            border-top: 1px solid #e8e8e8;
        }
        .descriptions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        .descriptions-item {
            display: flex;
            gap: 8px;
        }
        .descriptions-label {
            font-weight: 500;
            color: #2c3e50;
            min-width: 80px;
        }
        .descriptions-content {
            flex: 1;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <div class="test-header">
                <h1>EventInfoDisplay 组件测试</h1>
                <p>测试动态字段显示逻辑和过滤功能</p>
            </div>
            <div class="test-content">
                <div class="event-info-display">
                    <div class="event-info-content">
                        <div class="scrollable-content">
                            <!-- 事件核心信息 -->
                            <div class="info-card">
                                <div style="padding: 16px; border-bottom: 1px solid #e8e8e8; display: flex; justify-content: space-between; align-items: center;">
                                    <h3 style="margin: 0;">事件核心信息</h3>
                                    <span class="tag blue">处理中</span>
                                </div>
                                <div style="padding: 16px;">
                                    <div class="descriptions">
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">事件编号:</span>
                                            <span class="descriptions-content event-code">EVT2025070200000178</span>
                                        </div>
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">事件名称:</span>
                                            <span class="descriptions-content event-name">患者跌倒事件</span>
                                        </div>
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">事件分类:</span>
                                            <span class="descriptions-content"><span class="tag blue">跌倒事件</span></span>
                                        </div>
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">发生时间:</span>
                                            <span class="descriptions-content event-time">2025/7/2 00:32:00</span>
                                        </div>
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">发生科室:</span>
                                            <span class="descriptions-content event-dept">心血管内科</span>
                                        </div>
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">事件级别:</span>
                                            <span class="descriptions-content"><span class="tag red">I级</span></span>
                                        </div>
                                        <div class="descriptions-item">
                                            <span class="descriptions-label">伤害程度:</span>
                                            <span class="descriptions-content"><span class="tag orange">轻微伤害</span></span>
                                        </div>
                                    </div>

                                    <div class="divider"></div>

                                    <div class="event-summary-section">
                                        <h4>事件概述</h4>
                                        <div class="summary-content">
                                            患者在病房内行走时不慎跌倒，造成轻微擦伤。事发时护理人员正在其他病房进行护理工作，患者独自行走未有人陪护。
                                        </div>
                                    </div>

                                    <div class="divider"></div>

                                    <div class="additional-notes-section">
                                        <h4>补充说明</h4>
                                        <div class="notes-content">
                                            已对患者进行全面检查，伤势较轻，已进行相应处理。需要加强对高风险患者的监护和陪护管理。
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 事件详细信息 -->
                            <div class="info-card">
                                <div style="padding: 16px; border-bottom: 1px solid #e8e8e8;">
                                    <div class="card-title-with-controls">
                                        <span style="font-weight: 500;">事件详细信息</span>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <div class="switch-container" :class="{ checked: showOnlyFilledFields }" @click="toggleFilter">
                                                <span v-if="showOnlyFilledFields">已填</span>
                                                <span v-else>全部</span>
                                            </div>
                                            <span class="tag blue">{{ filledFieldsCount }}/{{ totalFieldsCount }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="padding: 16px;">
                                    <div class="dynamic-fields-section">
                                        <div class="dynamic-fields-grid">
                                            <div
                                                v-for="field in filteredFields"
                                                :key="field.key"
                                                class="dynamic-field-item"
                                                :class="{
                                                    'empty-field': field.isEmpty,
                                                    'filled-field': field.hasValue
                                                }"
                                            >
                                                <div class="field-label">
                                                    {{ field.label }}
                                                    <span v-if="field.isEmpty && !showOnlyFilledFields" class="empty-indicator">（未填写）</span>
                                                </div>
                                                <div class="field-value">
                                                    <span class="text-field-value" :class="{ 'empty-value': field.isEmpty }">
                                                        {{ field.value || '-' }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp, ref, computed } = Vue;

        // 创建应用
        const app = createApp({
            setup() {
                const showOnlyFilledFields = ref(false);

                // 动态字段数据
                const dynamicFields = [
                    { key: 'location', label: '发生地点类别', value: '高危服区域(手术、介入、分娩室与血液透析室等)', isEmpty: false, hasValue: true },
                    { key: 'disease', label: '诊疗疾病类别', value: '损伤、中毒和外因的某些其他后果', isEmpty: false, hasValue: true },
                    { key: 'severity', label: '事件严重程度', value: '', isEmpty: true, hasValue: false },
                    { key: 'cause', label: '事件原因分析', value: '', isEmpty: true, hasValue: false },
                    { key: 'prevention', label: '预防措施', value: '', isEmpty: true, hasValue: false },
                    { key: 'witness', label: '目击人员', value: '', isEmpty: true, hasValue: false },
                    { key: 'equipment', label: '相关设备', value: '', isEmpty: true, hasValue: false },
                    { key: 'medicine', label: '相关药品', value: '', isEmpty: true, hasValue: false }
                ];

                // 计算属性
                const filteredFields = computed(() => {
                    if (showOnlyFilledFields.value) {
                        return dynamicFields.filter(field => field.hasValue);
                    }
                    return dynamicFields;
                });

                const filledFieldsCount = computed(() => {
                    return dynamicFields.filter(field => field.hasValue).length;
                });

                const totalFieldsCount = computed(() => {
                    return dynamicFields.length;
                });

                // 方法
                const toggleFilter = () => {
                    showOnlyFilledFields.value = !showOnlyFilledFields.value;
                };

                return {
                    showOnlyFilledFields,
                    filteredFields,
                    filledFieldsCount,
                    totalFieldsCount,
                    toggleFilter
                };
            }
        });

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
