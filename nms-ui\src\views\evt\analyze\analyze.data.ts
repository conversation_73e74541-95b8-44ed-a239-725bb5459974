import { BasicColumn } from '@/components/Table'
import { FormSchema } from '@/components/Table'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '任务编号',
    dataIndex: 'taskCode',
    width: 120,
    fixed: 'left',
  },
  {
    title: '原始事件编号',
    dataIndex: 'eventId',
    width: 160,
    customRender: ({ record }) => {
      // 如果eventId是完整的事件编号，直接显示
      // 如果不是，可以添加前缀或格式化
      const eventId = record.eventId || '-'
      return h('span', {
        style: {
          color: '#1890ff',
          fontWeight: '500',
          cursor: 'pointer'
        },
        title: `点击查看事件详情: ${eventId}`
      }, eventId)
    },
  },
  {
    title: '事件名称',
    dataIndex: 'eventName',
    width: 200,
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
    width: 120,
  },
  {
    title: '分析状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      const statusMap = {
        pending: { text: '待分析', color: 'orange' },
        in_progress: { text: '分析中', color: 'blue' },
        completed: { text: '已完成', color: 'green' },
        cancelled: { text: '已取消', color: 'red' }
      }
      const status = statusMap[record.status] || { text: record.status, color: 'default' }
      return h(Tag, { color: status.color }, () => status.text)
    },
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    width: 80,
    customRender: ({ record }) => {
      const priorityMap = {
        urgent: { text: '紧急', color: 'red' },
        high: { text: '高', color: 'orange' },
        medium: { text: '中', color: 'blue' },
        low: { text: '低', color: 'green' }
      }
      const priority = priorityMap[record.priority] || { text: record.priority, color: 'default' }
      return h(Tag, { color: priority.color }, () => priority.text)
    },
  },
  {
    title: '分析人员',
    dataIndex: 'analyzerName',
    width: 120,
  },
  {
    title: '分配时间',
    dataIndex: 'assignTime',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm',
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm',
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'taskCode',
    label: '任务编号',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'eventId',
    label: '原始事件编号',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'eventName',
    label: '事件名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'eventType',
    label: '事件类型',
    component: 'ApiSelect',
    componentProps: {
      api: () => import('@/api/evt/dict').then(m => m.getEventTypeList()),
      labelField: 'label',
      valueField: 'value',
    },
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '分析状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '待分析', value: 'pending' },
        { label: '分析中', value: 'in_progress' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    componentProps: {
      options: [
        { label: '紧急', value: 'urgent' },
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'analyzerName',
    label: '分析人员',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'assignTimeRange',
    label: '分配时间',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
  {
    field: 'deadlineRange',
    label: '截止时间',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
]

// ============================ 知识库数据 ============================

import type { KnowledgeItem } from '@/utils/knowledgeSearch'

/**
 * 扩展的知识库数据
 * 包含常见原因、改进对策、相似案例、操作指南等
 */
export const knowledgeDatabase: KnowledgeItem[] = [
  // 药品相关原因
  {
    id: 'cause_drug_001',
    title: '药品标识不清晰',
    description: '药品包装相似，标识不够明显，容易导致拿错药品',
    content: '药品包装设计相似，特别是同一厂家的不同药品，在包装颜色、字体大小等方面区别不明显，护理人员在忙碌时容易混淆。',
    category: 'equipment',
    subCategory: 'drug_packaging',
    tags: ['药品安全', '包装设计', '视觉识别', '给药错误'],
    quality: 5,
    usageCount: 45,
    successRate: 0.85,
    relatedItems: ['measure_drug_001', 'case_drug_001'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-10'),
    source: '药事管理委员会',
    author: '张药师',
    type: 'cause'
  },
  {
    id: 'cause_drug_002',
    title: '双人核对制度执行不到位',
    description: '给药前未严格执行双人核对，或核对流于形式',
    content: '在繁忙的临床工作中，护理人员可能因为时间紧张或人手不足，未能严格执行双人核对制度，或者核对时注意力不集中。',
    category: 'management',
    subCategory: 'verification_process',
    tags: ['双人核对', '制度执行', '给药安全', '流程管理'],
    quality: 5,
    usageCount: 38,
    successRate: 0.92,
    relatedItems: ['measure_drug_002', 'guideline_001'],
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-02-08'),
    source: '护理部',
    author: '李护士长',
    type: 'cause'
  },
  {
    id: 'cause_people_001',
    title: '新员工培训不充分',
    description: '新入职护理人员对药品使用流程和安全要求不够熟悉',
    content: '新员工入职培训时间短，对医院的药品管理制度、给药流程、安全要求等掌握不够充分，缺乏实际操作经验。',
    category: 'people',
    subCategory: 'training',
    tags: ['新员工', '培训体系', '能力建设', '知识掌握'],
    quality: 4,
    usageCount: 32,
    successRate: 0.78,
    relatedItems: ['measure_training_001', 'guideline_002'],
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-02-05'),
    source: '人力资源部',
    author: '王主任',
    type: 'cause'
  },
  {
    id: 'cause_env_001',
    title: '工作环境嘈杂干扰',
    description: '病房环境嘈杂，影响护理人员注意力集中',
    content: '病房内患者较多，家属探视频繁，各种医疗设备报警声此起彼伏，造成工作环境嘈杂，影响护理人员专注度。',
    category: 'environment',
    subCategory: 'noise_control',
    tags: ['工作环境', '噪音干扰', '注意力', '专注度'],
    quality: 4,
    usageCount: 28,
    successRate: 0.72,
    relatedItems: ['measure_env_001'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-12'),
    source: '质量管理科',
    author: '陈主任',
    type: 'cause'
  },
  {
    id: 'cause_comm_001',
    title: '医护沟通不充分',
    description: '医生与护士之间沟通不充分，信息传递不准确',
    content: '医生开具医嘱时说明不够详细，护士对医嘱理解有偏差，或者交班时信息传递不完整，导致执行错误。',
    category: 'communication',
    subCategory: 'medical_communication',
    tags: ['医护沟通', '信息传递', '医嘱执行', '交班制度'],
    quality: 4,
    usageCount: 35,
    successRate: 0.80,
    relatedItems: ['measure_comm_001', 'guideline_003'],
    createdAt: new Date('2024-01-30'),
    updatedAt: new Date('2024-02-09'),
    source: '医务科',
    author: '刘医生',
    type: 'cause'
  },

  // 改进对策
  {
    id: 'measure_drug_001',
    title: '加强药品标识管理',
    description: '统一药品标识规范，使用醒目的颜色和字体区分不同药品',
    content: '制定统一的药品标识标准，对易混淆药品使用不同颜色标识，增大字体，添加警示标志，提高视觉识别度。',
    category: 'equipment',
    subCategory: 'drug_identification',
    tags: ['药品标识', '视觉管理', '标准化', '安全管理'],
    quality: 5,
    usageCount: 42,
    successRate: 0.88,
    relatedItems: ['cause_drug_001', 'guideline_004'],
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-02-11'),
    source: '药剂科',
    author: '赵药师',
    type: 'measure'
  },
  {
    id: 'measure_drug_002',
    title: '强化双人核对制度',
    description: '建立严格的双人核对机制，确保每次给药都有两人确认',
    content: '制定详细的双人核对流程，明确核对内容和标准，建立核对记录制度，定期检查执行情况，对违规行为进行处罚。',
    category: 'management',
    subCategory: 'verification_system',
    tags: ['双人核对', '制度建设', '流程优化', '质量控制'],
    quality: 5,
    usageCount: 40,
    successRate: 0.95,
    relatedItems: ['cause_drug_002', 'guideline_001'],
    createdAt: new Date('2024-01-22'),
    updatedAt: new Date('2024-02-07'),
    source: '护理部',
    author: '孙护士长',
    type: 'measure'
  },
  {
    id: 'measure_training_001',
    title: '完善新员工培训体系',
    description: '建立系统性的新员工培训计划，包括理论学习和实践操作',
    content: '设计分阶段培训计划，包括入职培训、岗位培训、技能考核等环节，配备专门的带教老师，建立培训档案和考核记录。',
    category: 'people',
    subCategory: 'training_system',
    tags: ['培训体系', '能力建设', '新员工', '技能提升'],
    quality: 4,
    usageCount: 35,
    successRate: 0.82,
    relatedItems: ['cause_people_001', 'guideline_002'],
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-02-06'),
    source: '教育培训科',
    author: '马主任',
    type: 'measure'
  },
  {
    id: 'measure_env_001',
    title: '优化工作环境管理',
    description: '改善病房环境，减少噪音干扰，创造安静的工作环境',
    content: '设置安静时段，控制探视时间，优化设备报警设置，使用隔音材料，建立环境管理制度。',
    category: 'environment',
    subCategory: 'environment_optimization',
    tags: ['环境管理', '噪音控制', '工作环境', '专注度提升'],
    quality: 4,
    usageCount: 25,
    successRate: 0.75,
    relatedItems: ['cause_env_001'],
    createdAt: new Date('2024-02-03'),
    updatedAt: new Date('2024-02-13'),
    source: '后勤保障部',
    author: '周主任',
    type: 'measure'
  },
  {
    id: 'measure_comm_001',
    title: '建立标准化沟通机制',
    description: '制定医护沟通标准，使用SBAR沟通模式，确保信息准确传递',
    content: '推行SBAR（情况-背景-评估-建议）沟通模式，制定沟通检查清单，建立信息确认机制，定期培训沟通技巧。',
    category: 'communication',
    subCategory: 'communication_standard',
    tags: ['沟通标准', 'SBAR模式', '信息传递', '团队协作'],
    quality: 5,
    usageCount: 33,
    successRate: 0.85,
    relatedItems: ['cause_comm_001', 'guideline_003'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-10'),
    source: '质量管理科',
    author: '吴主任',
    type: 'measure'
  },

  // 相似案例
  {
    id: 'case_drug_001',
    title: '某三甲医院药品给药错误案例',
    description: '护士在给药过程中拿错药品，导致患者用药错误的典型案例',
    content: '2024年1月，某三甲医院ICU发生一起给药错误事件。护士在为患者准备静脉用药时，由于两种药品包装相似，误将A药当作B药给患者使用。幸运的是及时发现并处理，患者未出现严重不良反应。',
    category: 'equipment',
    subCategory: 'drug_error_case',
    tags: ['给药错误', '包装相似', 'ICU', '案例分析'],
    quality: 5,
    usageCount: 28,
    successRate: 0.90,
    relatedItems: ['cause_drug_001', 'measure_drug_001'],
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-02-08'),
    source: '不良事件报告系统',
    author: '安全管理员',
    type: 'case'
  },
  {
    id: 'case_training_001',
    title: '新员工培训不足导致的操作错误',
    description: '新入职护士因培训不充分导致的操作失误案例',
    content: '某医院新入职护士在独立值班第二周发生给药剂量错误。调查发现该护士对医院的给药流程和剂量计算方法掌握不够熟练，培训时间较短且缺乏充分的实践机会。',
    category: 'people',
    subCategory: 'training_case',
    tags: ['新员工', '培训不足', '操作错误', '能力建设'],
    quality: 4,
    usageCount: 22,
    successRate: 0.85,
    relatedItems: ['cause_people_001', 'measure_training_001'],
    createdAt: new Date('2024-01-26'),
    updatedAt: new Date('2024-02-04'),
    source: '护理部',
    author: '质量管理员',
    type: 'case'
  },

  // 操作指南
  {
    id: 'guideline_001',
    title: '双人核对操作指南',
    description: '详细的双人核对操作流程和注意事项',
    content: '1. 核对患者身份：姓名、床号、住院号；2. 核对药品信息：药名、剂量、浓度、用法；3. 核对医嘱：给药时间、给药途径；4. 双方签字确认；5. 记录核对过程。',
    category: 'management',
    subCategory: 'operation_guide',
    tags: ['双人核对', '操作指南', '标准流程', '质量控制'],
    quality: 5,
    usageCount: 50,
    successRate: 0.95,
    relatedItems: ['cause_drug_002', 'measure_drug_002'],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-02-05'),
    source: '护理部',
    author: '护理标准委员会',
    type: 'guideline'
  },
  {
    id: 'guideline_002',
    title: '新员工培训标准',
    description: '新入职护理人员的培训要求和考核标准',
    content: '培训内容包括：医院规章制度、护理操作规范、药品安全知识、沟通技巧等。培训时间不少于40学时，必须通过理论考试和实践考核才能独立上岗。',
    category: 'people',
    subCategory: 'training_standard',
    tags: ['新员工培训', '培训标准', '考核要求', '能力评估'],
    quality: 4,
    usageCount: 38,
    successRate: 0.88,
    relatedItems: ['cause_people_001', 'measure_training_001'],
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-02-03'),
    source: '人力资源部',
    author: '培训管理委员会',
    type: 'guideline'
  },
  {
    id: 'guideline_003',
    title: 'SBAR沟通标准',
    description: '医护人员标准化沟通模式的使用指南',
    content: 'S-Situation（情况）：简述当前情况；B-Background（背景）：提供相关背景信息；A-Assessment（评估）：说明评估结果；R-Recommendation（建议）：提出具体建议。',
    category: 'communication',
    subCategory: 'communication_guide',
    tags: ['SBAR沟通', '标准化沟通', '团队协作', '信息传递'],
    quality: 5,
    usageCount: 45,
    successRate: 0.92,
    relatedItems: ['cause_comm_001', 'measure_comm_001'],
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-02-07'),
    source: '医务科',
    author: '沟通管理委员会',
    type: 'guideline'
  },
  {
    id: 'guideline_004',
    title: '药品标识管理规范',
    description: '医院药品标识的统一标准和管理要求',
    content: '药品标识应包括：药品名称、规格、浓度、有效期等关键信息。高危药品使用红色标识，易混淆药品使用不同颜色区分，字体大小不小于12号。',
    category: 'equipment',
    subCategory: 'drug_labeling',
    tags: ['药品标识', '标识规范', '视觉管理', '安全标识'],
    quality: 5,
    usageCount: 41,
    successRate: 0.90,
    relatedItems: ['cause_drug_001', 'measure_drug_001'],
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-02-09'),
    source: '药剂科',
    author: '药品管理委员会',
    type: 'guideline'
  }
]

/**
 * 知识库分类定义
 */
export const knowledgeCategories = [
  { key: 'people', name: '人员因素', color: '#1890ff', icon: 'user' },
  { key: 'equipment', name: '设备因素', color: '#52c41a', icon: 'tool' },
  { key: 'environment', name: '环境因素', color: '#faad14', icon: 'environment' },
  { key: 'management', name: '管理因素', color: '#722ed1', icon: 'setting' },
  { key: 'policy', name: '制度因素', color: '#eb2f96', icon: 'file-text' },
  { key: 'process', name: '流程因素', color: '#13c2c2', icon: 'flow-chart' },
  { key: 'communication', name: '沟通因素', color: '#f5222d', icon: 'message' },
  { key: 'documentation', name: '文档因素', color: '#fa8c16', icon: 'file' },
  { key: 'training', name: '培训因素', color: '#a0d911', icon: 'book' },
  { key: 'other', name: '其他因素', color: '#d9d9d9', icon: 'question' }
]

/**
 * 知识库子分类定义
 */
export const knowledgeSubCategories = [
  // 设备因素子分类
  { key: 'drug_packaging', name: '药品包装', parent: 'equipment' },
  { key: 'drug_identification', name: '药品标识', parent: 'equipment' },
  { key: 'drug_labeling', name: '药品标签', parent: 'equipment' },
  { key: 'medical_device', name: '医疗器械', parent: 'equipment' },

  // 管理因素子分类
  { key: 'verification_process', name: '核对流程', parent: 'management' },
  { key: 'verification_system', name: '核对制度', parent: 'management' },
  { key: 'quality_control', name: '质量控制', parent: 'management' },
  { key: 'operation_guide', name: '操作指南', parent: 'management' },

  // 人员因素子分类
  { key: 'training', name: '培训教育', parent: 'people' },
  { key: 'training_system', name: '培训体系', parent: 'people' },
  { key: 'training_standard', name: '培训标准', parent: 'people' },
  { key: 'skill_assessment', name: '技能评估', parent: 'people' },

  // 环境因素子分类
  { key: 'noise_control', name: '噪音控制', parent: 'environment' },
  { key: 'environment_optimization', name: '环境优化', parent: 'environment' },
  { key: 'workspace_design', name: '工作空间', parent: 'environment' },

  // 沟通因素子分类
  { key: 'medical_communication', name: '医护沟通', parent: 'communication' },
  { key: 'communication_standard', name: '沟通标准', parent: 'communication' },
  { key: 'communication_guide', name: '沟通指南', parent: 'communication' },

  // 案例分类
  { key: 'drug_error_case', name: '药品错误案例', parent: 'equipment' },
  { key: 'training_case', name: '培训案例', parent: 'people' },
  { key: 'communication_case', name: '沟通案例', parent: 'communication' }
]

export const formSchema: FormSchema[] = [
  {
    field: 'eventId',
    label: '关联事件',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => import('@/api/evt/info').then(m => m.getEventList()),
      labelField: 'eventName',
      valueField: 'id',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
    },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    required: true,
    defaultValue: 'medium',
    componentProps: {
      options: [
        { label: '紧急', value: 'urgent' },
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
  },
  {
    field: 'analyzerId',
    label: '分析人员',
    component: 'ApiSelect',
    componentProps: {
      api: () => import('@/api/system/user').then(m => m.getUserList()),
      labelField: 'nickname',
      valueField: 'id',
      showSearch: true,
    },
  },
  {
    field: 'deadline',
    label: '截止时间',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    field: 'description',
    label: '任务描述',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
      maxlength: 500,
      showCount: true,
    },
  },
]
