/**
 * 事件分析管理API接口
 * 
 * 基于InfoModal.vue业务流程分离，提供事件分析管理的API接口定义
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-14
 */

import { defHttp } from '@/utils/http/axios'
import type { 
  EventAnalysis,
  AnalysisStatus,
  Participant,
  AnalysisCause,
  AnalysisMeasure,
  AnalysisToolResult,
  ApiResponse,
  PageResponse,
  QueryParams
} from '@/types/moduleCommunication'

// ============================ 类型定义 ============================

/**
 * 分析任务查询参数
 */
export interface AnalysisTaskQueryParams extends QueryParams {
  eventId?: string
  status?: AnalysisStatus
  assignedTo?: string
  priority?: string
  startDate?: string
  endDate?: string
  keyword?: string
}

/**
 * 创建分析任务参数
 */
export interface CreateAnalysisTaskParams {
  eventId: string
  title: string
  description?: string
  priority?: 'low' | 'normal' | 'high'
  assignedTo?: string
  deadline?: string
}

/**
 * 知识库搜索参数
 */
export interface KnowledgeSearchParams {
  query: string
  type?: 'all' | 'cause' | 'measure' | 'case' | 'guideline'
  category?: string[]
  qualityRange?: [number, number]
  usageFrequency?: 'high' | 'medium' | 'low'
  dateRange?: [string, string]
  matchOptions?: string[]
  page?: number
  pageSize?: number
  sortBy?: 'relevance' | 'quality' | 'usage' | 'date'
  sortOrder?: 'asc' | 'desc'
}

/**
 * 知识库项目
 */
export interface KnowledgeItem {
  id: string
  title: string
  content: string
  type: 'cause' | 'measure' | 'case' | 'guideline'
  category: string
  quality: number
  usageCount: number
  successRate: number
  tags: string[]
  createdAt: string
  updatedAt: string
  createdBy: string
  department?: string
  isFavorited?: boolean
  referenceCount?: number
  lastUsed?: string
}

/**
 * 知识库搜索结果
 */
export interface KnowledgeSearchResult {
  items: KnowledgeItem[]
  total: number
  page: number
  pageSize: number
  searchTime: number
  facets: {
    categories: Array<{ key: string; name: string; count: number }>
    types: Array<{ key: string; name: string; count: number }>
    qualityDistribution: Array<{ range: string; count: number }>
  }
  suggestions?: string[]
}

/**
 * 知识引用记录
 */
export interface KnowledgeReference {
  id: string
  knowledgeId: string
  knowledgeTitle: string
  knowledgeType: string
  referencedAt: string
  referencedBy: string
  context: {
    eventId?: string
    analysisId?: string
    taskId?: string
    department?: string
  }
}

/**
 * 更新分析数据参数
 */
export interface UpdateAnalysisParams {
  meetingTime?: string
  meetingLocation?: string
  participants?: Participant[]
  causes?: AnalysisCause[]
  measures?: AnalysisMeasure[]
  analysisTools?: AnalysisToolResult[]
  conclusion?: string
  recommendations?: string
}

/**
 * 完成分析参数
 */
export interface CompleteAnalysisParams {
  conclusion: string
  recommendations: string
  status: 'completed'
}

/**
 * 分析工具参数
 */
export interface AnalysisToolParams {
  toolType: 'fishbone' | 'five_why' | 'pdca' | 'fmea' | 'rca'
  eventId: string
  analysisId: string
  toolData: any
}

// ============================ API接口定义 ============================

/**
 * 事件分析管理API类
 */
export class EventAnalysisAPI {
  
  // ============================ 分析任务管理 ============================
  
  /**
   * 获取分析任务列表
   */
  static async getAnalysisTasks(params?: AnalysisTaskQueryParams): Promise<PageResponse<any>> {
    return defHttp.get<PageResponse<any>>({
      url: '/api/evt/analysis/tasks',
      params
    })
  }
  
  /**
   * 创建分析任务
   */
  static async createAnalysisTask(data: CreateAnalysisTaskParams): Promise<ApiResponse<any>> {
    return defHttp.post<ApiResponse<any>>({
      url: '/api/evt/analysis/tasks',
      data
    })
  }
  
  /**
   * 获取分析任务详情
   */
  static async getAnalysisTask(taskId: string): Promise<ApiResponse<any>> {
    return defHttp.get<ApiResponse<any>>({
      url: `/api/evt/analysis/tasks/${taskId}`
    })
  }
  
  /**
   * 更新分析任务
   */
  static async updateAnalysisTask(taskId: string, data: Partial<CreateAnalysisTaskParams>): Promise<ApiResponse<any>> {
    return defHttp.put<ApiResponse<any>>({
      url: `/api/evt/analysis/tasks/${taskId}`,
      data
    })
  }
  
  /**
   * 删除分析任务
   */
  static async deleteAnalysisTask(taskId: string): Promise<ApiResponse<void>> {
    return defHttp.delete<ApiResponse<void>>({
      url: `/api/evt/analysis/tasks/${taskId}`
    })
  }
  
  // ============================ 分析数据管理 ============================
  
  /**
   * 获取分析详情
   */
  static async getAnalysisDetails(analysisId: string): Promise<ApiResponse<EventAnalysis>> {
    return defHttp.get<ApiResponse<EventAnalysis>>({
      url: `/api/evt/analysis/${analysisId}`
    })
  }
  
  /**
   * 更新分析数据
   */
  static async updateAnalysisData(analysisId: string, data: UpdateAnalysisParams): Promise<ApiResponse<EventAnalysis>> {
    return defHttp.put<ApiResponse<EventAnalysis>>({
      url: `/api/evt/analysis/${analysisId}`,
      data
    })
  }
  
  /**
   * 完成分析
   */
  static async completeAnalysis(analysisId: string, data: CompleteAnalysisParams): Promise<ApiResponse<EventAnalysis>> {
    return defHttp.put<ApiResponse<EventAnalysis>>({
      url: `/api/evt/analysis/${analysisId}/complete`,
      data
    })
  }
  
  // ============================ 参会人员管理 ============================
  
  /**
   * 获取参会人员列表
   */
  static async getParticipants(analysisId: string): Promise<ApiResponse<Participant[]>> {
    return defHttp.get<ApiResponse<Participant[]>>({
      url: `/api/evt/analysis/${analysisId}/participants`
    })
  }
  
  /**
   * 添加参会人员
   */
  static async addParticipant(analysisId: string, participant: Omit<Participant, 'id'>): Promise<ApiResponse<Participant>> {
    return defHttp.post<ApiResponse<Participant>>({
      url: `/api/evt/analysis/${analysisId}/participants`,
      data: participant
    })
  }
  
  /**
   * 更新参会人员
   */
  static async updateParticipant(analysisId: string, participantId: string, data: Partial<Participant>): Promise<ApiResponse<Participant>> {
    return defHttp.put<ApiResponse<Participant>>({
      url: `/api/evt/analysis/${analysisId}/participants/${participantId}`,
      data
    })
  }
  
  /**
   * 删除参会人员
   */
  static async removeParticipant(analysisId: string, participantId: string): Promise<ApiResponse<void>> {
    return defHttp.delete<ApiResponse<void>>({
      url: `/api/evt/analysis/${analysisId}/participants/${participantId}`
    })
  }
  
  // ============================ 原因分析管理 ============================
  
  /**
   * 获取原因分析列表
   */
  static async getCauses(analysisId: string): Promise<ApiResponse<AnalysisCause[]>> {
    return defHttp.get<ApiResponse<AnalysisCause[]>>({
      url: `/api/evt/analysis/${analysisId}/causes`
    })
  }
  
  /**
   * 添加原因分析
   */
  static async addCause(analysisId: string, cause: Omit<AnalysisCause, 'id'>): Promise<ApiResponse<AnalysisCause>> {
    return defHttp.post<ApiResponse<AnalysisCause>>({
      url: `/api/evt/analysis/${analysisId}/causes`,
      data: cause
    })
  }
  
  /**
   * 更新原因分析
   */
  static async updateCause(analysisId: string, causeId: string, data: Partial<AnalysisCause>): Promise<ApiResponse<AnalysisCause>> {
    return defHttp.put<ApiResponse<AnalysisCause>>({
      url: `/api/evt/analysis/${analysisId}/causes/${causeId}`,
      data
    })
  }
  
  /**
   * 删除原因分析
   */
  static async removeCause(analysisId: string, causeId: string): Promise<ApiResponse<void>> {
    return defHttp.delete<ApiResponse<void>>({
      url: `/api/evt/analysis/${analysisId}/causes/${causeId}`
    })
  }
  
  // ============================ 对策措施管理 ============================
  
  /**
   * 获取对策措施列表
   */
  static async getMeasures(analysisId: string): Promise<ApiResponse<AnalysisMeasure[]>> {
    return defHttp.get<ApiResponse<AnalysisMeasure[]>>({
      url: `/api/evt/analysis/${analysisId}/measures`
    })
  }
  
  /**
   * 添加对策措施
   */
  static async addMeasure(analysisId: string, measure: Omit<AnalysisMeasure, 'id'>): Promise<ApiResponse<AnalysisMeasure>> {
    return defHttp.post<ApiResponse<AnalysisMeasure>>({
      url: `/api/evt/analysis/${analysisId}/measures`,
      data: measure
    })
  }
  
  /**
   * 更新对策措施
   */
  static async updateMeasure(analysisId: string, measureId: string, data: Partial<AnalysisMeasure>): Promise<ApiResponse<AnalysisMeasure>> {
    return defHttp.put<ApiResponse<AnalysisMeasure>>({
      url: `/api/evt/analysis/${analysisId}/measures/${measureId}`,
      data
    })
  }
  
  /**
   * 删除对策措施
   */
  static async removeMeasure(analysisId: string, measureId: string): Promise<ApiResponse<void>> {
    return defHttp.delete<ApiResponse<void>>({
      url: `/api/evt/analysis/${analysisId}/measures/${measureId}`
    })
  }
  
  // ============================ 分析工具管理 ============================
  
  /**
   * 获取分析工具列表
   */
  static async getAnalysisTools(): Promise<ApiResponse<any[]>> {
    return defHttp.get<ApiResponse<any[]>>({
      url: '/api/evt/analysis/tools'
    })
  }
  
  /**
   * 使用分析工具
   */
  static async useAnalysisTool(params: AnalysisToolParams): Promise<ApiResponse<AnalysisToolResult>> {
    return defHttp.post<ApiResponse<AnalysisToolResult>>({
      url: `/api/evt/analysis/${params.analysisId}/tools/${params.toolType}`,
      data: {
        eventId: params.eventId,
        toolData: params.toolData
      }
    })
  }
  
  /**
   * 获取工具分析结果
   */
  static async getToolResult(analysisId: string, toolType: string): Promise<ApiResponse<AnalysisToolResult>> {
    return defHttp.get<ApiResponse<AnalysisToolResult>>({
      url: `/api/evt/analysis/${analysisId}/tools/${toolType}/result`
    })
  }
  
  // ============================ 知识库管理 ============================
  
  /**
   * 获取分析知识库
   */
  static async getKnowledgeBase(params?: {
    eventType?: string
    keyword?: string
    type?: 'cause' | 'measure' | 'tool' | 'case'
    pageNum?: number
    pageSize?: number
  }): Promise<PageResponse<any>> {
    return defHttp.get<PageResponse<any>>({
      url: '/api/evt/analysis/knowledge',
      params
    })
  }
  
  /**
   * 搜索知识库
   */
  static async searchKnowledge(keyword: string, filters?: {
    eventType?: string
    type?: string
    tags?: string[]
  }): Promise<ApiResponse<any[]>> {
    return defHttp.get<ApiResponse<any[]>>({
      url: '/api/evt/analysis/knowledge/search',
      params: {
        keyword,
        ...filters
      }
    })
  }
  
  /**
   * 获取推荐知识
   */
  static async getRecommendedKnowledge(eventId: string, analysisId?: string): Promise<ApiResponse<any[]>> {
    return defHttp.get<ApiResponse<any[]>>({
      url: '/api/evt/analysis/knowledge/recommend',
      params: {
        eventId,
        analysisId
      }
    })
  }
  
  // ============================ 数据同步接口 ============================
  
  /**
   * 同步事件信息
   */
  static async syncEventInfo(eventId: string, eventInfo: any): Promise<ApiResponse<void>> {
    return defHttp.post<ApiResponse<void>>({
      url: `/api/evt/analysis/sync/event/${eventId}`,
      data: eventInfo
    })
  }
  
  /**
   * 获取分析结果（供改进模块使用）
   */
  static async getAnalysisResult(analysisId: string): Promise<ApiResponse<EventAnalysis>> {
    return defHttp.get<ApiResponse<EventAnalysis>>({
      url: `/api/evt/analysis/${analysisId}/result`
    })
  }
  
  /**
   * 获取事件分析状态
   */
  static async getEventAnalysisStatus(eventId: string): Promise<ApiResponse<{
    hasAnalysis: boolean
    analysisId?: string
    status?: AnalysisStatus
    progress?: number
  }>> {
    return defHttp.get<ApiResponse<any>>({
      url: `/api/evt/analysis/event/${eventId}/status`
    })
  }
  
  // ============================ 统计分析接口 ============================
  
  /**
   * 获取分析统计数据
   */
  static async getAnalysisStatistics(params?: {
    startDate?: string
    endDate?: string
    eventType?: string
    department?: string
  }): Promise<ApiResponse<{
    totalAnalysis: number
    completedAnalysis: number
    pendingAnalysis: number
    averageAnalysisTime: number
    topCauses: any[]
    topMeasures: any[]
  }>> {
    return defHttp.get<ApiResponse<any>>({
      url: '/api/evt/analysis/statistics',
      params
    })
  }
  
  /**
   * 获取分析效果评估
   */
  static async getAnalysisEffectiveness(analysisId: string): Promise<ApiResponse<{
    effectivenessScore: number
    implementationRate: number
    satisfactionScore: number
    recommendations: string[]
  }>> {
    return defHttp.get<ApiResponse<any>>({
      url: `/api/evt/analysis/${analysisId}/effectiveness`
    })
  }
}

// ============================ 导出便捷函数 ============================

/**
 * 获取分析任务列表
 */
export const getAnalysisTasks = EventAnalysisAPI.getAnalysisTasks

/**
 * 创建分析任务
 */
export const createAnalysisTask = EventAnalysisAPI.createAnalysisTask

/**
 * 获取分析详情
 */
export const getAnalysisDetails = EventAnalysisAPI.getAnalysisDetails

/**
 * 更新分析数据
 */
export const updateAnalysisData = EventAnalysisAPI.updateAnalysisData

/**
 * 完成分析
 */
export const completeAnalysis = EventAnalysisAPI.completeAnalysis

/**
 * 获取知识库
 */
export const getKnowledgeBase = EventAnalysisAPI.getKnowledgeBase

/**
 * 同步事件信息
 */
export const syncEventInfo = EventAnalysisAPI.syncEventInfo

/**
 * 获取分析结果
 */
export const getAnalysisResult = EventAnalysisAPI.getAnalysisResult

/**
 * 获取分析统计
 */
export const getAnalysisStatistics = EventAnalysisAPI.getAnalysisStatistics

// 分析任务相关接口
export interface AnalyzeTaskPageParams {
  taskCode?: string
  eventId?: string
  eventName?: string
  eventType?: string
  status?: string
  priority?: string
  analyzerName?: string
  assignTimeRange?: string[]
  deadlineRange?: string[]
  pageNo?: number
  pageSize?: number
}

export interface AnalyzeTaskVO {
  id: string
  taskCode: string
  eventId: string
  eventName: string
  eventType: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'urgent' | 'high' | 'medium' | 'low'
  analyzerId: string
  analyzerName: string
  assignTime: string
  deadline: string
  createTime: string
  updateTime?: string
  progress?: number
  estimatedHours?: number
  remark?: string
}

// 获取分析任务分页列表
export const getAnalyzeTaskPage = (params: AnalyzeTaskPageParams) => {
  return defHttp.get<PageResult<AnalyzeTaskVO>>({
    url: '/evt/analyze/task/page',
    params,
  })
}

// 删除分析任务
export const deleteAnalyzeTask = (id: string) => {
  return defHttp.delete({
    url: `/evt/analyze/task/${id}`,
  })
}

// 创建分析任务
export const createAnalyzeTask = (data: { eventId: string; priority?: string }) => {
  return defHttp.post<AnalyzeTaskVO>({
    url: '/evt/analyze/task/create',
    data,
  })
}

// 保存分析数据
export const saveAnalysisData = (data: any) => {
  return defHttp.post({
    url: '/evt/analyze/save',
    data,
  })
}

// 获取知识库原因列表
export const getKnowledgeCauses = (params: { keyword?: string; eventType?: string }) => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/causes',
    params,
  })
}

// 获取知识库对策列表
export const getKnowledgeMeasures = (params: { keyword?: string; causeId?: string }) => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/measures',
    params,
  })
}

// 获取事件信息详情（通过事件ID）
export const getEventInfo = (eventId: string) => {
  return defHttp.get({
    url: `/evt/info/get?id=${eventId}`,
  })
}

// 获取分析任务详情（通过任务ID）
export const getAnalyzeTaskDetail = (taskId: string) => {
  return defHttp.get({
    url: `/evt/analyze/task/${taskId}`,
  })
}

// ========================== PDCA循环管理API ==========================

// 保存PDCA数据
export const savePDCAData = (data: {
  eventId: string
  analysisId?: string
  toolType: 'pdca'
  toolName: string
  toolData: any
  autoSave?: boolean
}) => {
  return defHttp.post({
    url: '/api/evt/analysis-manager/tool/pdca/save',
    data,
  })
}

// 获取PDCA数据
export const getPDCAData = (params: {
  eventId: string
  analysisId?: string
}) => {
  return defHttp.get({
    url: '/api/evt/analysis-manager/tool/pdca/get',
    params,
  })
}

// 更新PDCA数据
export const updatePDCAData = (data: {
  id: string
  eventId: string
  analysisId?: string
  toolData: any
}) => {
  return defHttp.put({
    url: '/api/evt/analysis-manager/tool/pdca/update',
    data,
  })
}

// 开始PDCA阶段
export const startPDCAPhase = (data: {
  pdcaId: string
  cycleId: string
  phase: 'plan' | 'do' | 'check' | 'act'
}) => {
  return defHttp.post({
    url: '/api/evt/analysis-manager/tool/pdca/phase/start',
    data,
  })
}

// 完成PDCA阶段
export const completePDCAPhase = (data: {
  pdcaId: string
  cycleId: string
  phase: 'plan' | 'do' | 'check' | 'act'
  deliverables: string[]
  notes?: string
}) => {
  return defHttp.post({
    url: '/api/evt/analysis-manager/tool/pdca/phase/complete',
    data,
  })
}

// 获取PDCA进度
export const getPDCAProgress = (params: {
  pdcaId: string
  cycleId?: string
}) => {
  return defHttp.get({
    url: '/api/evt/analysis-manager/tool/pdca/phase/progress',
    params,
  })
}

// 创建PDCA任务
export const createPDCATask = (data: {
  pdcaId: string
  cycleId: string
  phase: 'plan' | 'do' | 'check' | 'act'
  task: {
    name: string
    description: string
    assigneeId: string
    priority: 'low' | 'medium' | 'high' | 'urgent'
    estimatedHours: number
    startDate?: string
    endDate?: string
    deliverables: string[]
  }
}) => {
  return defHttp.post({
    url: '/api/evt/analysis-manager/tool/pdca/task/create',
    data,
  })
}

// 更新PDCA任务
export const updatePDCATask = (data: {
  taskId: string
  updates: {
    name?: string
    description?: string
    status?: 'not_started' | 'in_progress' | 'completed' | 'cancelled' | 'blocked'
    progress?: number
    actualHours?: number
    notes?: string
  }
}) => {
  return defHttp.put({
    url: '/api/evt/analysis-manager/tool/pdca/task/update',
    data,
  })
}

// 删除PDCA任务
export const deletePDCATask = (taskId: string) => {
  return defHttp.delete({
    url: `/api/evt/analysis-manager/tool/pdca/task/delete/${taskId}`,
  })
}

// 提交PDCA效果评估
export const submitPDCAEvaluation = (data: {
  pdcaId: string
  cycleId: string
  evaluation: {
    quantitativeMetrics: any
    qualitativeMetrics: any
    overallScore: number
    recommendation: 'excellent' | 'good' | 'fair' | 'poor'
    strengths: string[]
    weaknesses: string[]
    improvementActions: string[]
    bestPractices: string[]
    lessonsLearned: string[]
  }
}) => {
  return defHttp.post({
    url: '/api/evt/analysis-manager/tool/pdca/evaluation/submit',
    data,
  })
}

// 获取PDCA评估报告
export const getPDCAEvaluationReport = (params: {
  pdcaId: string
  cycleId?: string
  reportType?: 'progress' | 'phase_completion' | 'cycle_completion' | 'final'
}) => {
  return defHttp.get({
    url: '/api/evt/analysis-manager/tool/pdca/evaluation/report',
    params,
  })
}

// 获取PDCA模板列表
export const getPDCATemplates = (params: {
  category?: string
  eventType?: string
  keyword?: string
  pageNum?: number
  pageSize?: number
}) => {
  return defHttp.get({
    url: '/api/evt/analysis-manager/tool/pdca/templates',
    params,
  })
}

// 基于模板创建PDCA
export const createPDCAFromTemplate = (data: {
  templateId: string
  eventId: string
  analysisId?: string
  customizations: {
    title: string
    description: string
    teamMembers: string[]
    startDate: string
    endDate?: string
  }
}) => {
  return defHttp.post({
    url: '/api/evt/analysis-manager/tool/pdca/create-from-template',
    data,
  })
}

// ============================ 知识库API ============================

/**
 * 搜索知识库
 */
export const searchKnowledge = (params: KnowledgeSearchParams): Promise<ApiResponse<KnowledgeSearchResult>> => {
  return defHttp.post({
    url: '/evt/analyze/knowledge/search',
    data: params,
  })
}

/**
 * 获取知识库分类
 */
export const getKnowledgeCategories = (): Promise<ApiResponse<Array<{ key: string; name: string; color: string; count: number }>>> => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/categories',
  })
}

/**
 * 获取热门搜索词
 */
export const getPopularSearches = (): Promise<ApiResponse<string[]>> => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/popular-searches',
  })
}

/**
 * 获取搜索建议
 */
export const getSearchSuggestions = (query: string): Promise<ApiResponse<string[]>> => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/suggestions',
    params: { query },
  })
}

/**
 * 引用知识到分析中
 */
export const referenceKnowledge = (data: {
  knowledgeId: string
  analysisId: string
  referenceType: 'cause' | 'measure' | 'reference'
  context?: any
}): Promise<ApiResponse<KnowledgeReference>> => {
  return defHttp.post({
    url: '/evt/analyze/knowledge/reference',
    data,
  })
}

/**
 * 获取引用历史
 */
export const getReferenceHistory = (params: {
  analysisId?: string
  userId?: string
  page?: number
  pageSize?: number
}): Promise<ApiResponse<PageResponse<KnowledgeReference>>> => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/reference-history',
    params,
  })
}

/**
 * 收藏/取消收藏知识
 */
export const toggleKnowledgeFavorite = (knowledgeId: string, action: 'add' | 'remove'): Promise<ApiResponse<void>> => {
  return defHttp.post({
    url: `/evt/analyze/knowledge/${knowledgeId}/favorite`,
    data: { action },
  })
}

/**
 * 评分知识
 */
export const rateKnowledge = (knowledgeId: string, rating: number, comment?: string): Promise<ApiResponse<void>> => {
  return defHttp.post({
    url: `/evt/analyze/knowledge/${knowledgeId}/rate`,
    data: { rating, comment },
  })
}

/**
 * 获取用户收藏的知识
 */
export const getFavoriteKnowledge = (params: {
  page?: number
  pageSize?: number
  type?: string
}): Promise<ApiResponse<PageResponse<KnowledgeItem>>> => {
  return defHttp.get({
    url: '/evt/analyze/knowledge/favorites',
    params,
  })
}
