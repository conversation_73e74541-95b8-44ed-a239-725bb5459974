<template>
  <div class="event-info-test">
    <h3>EventInfoDisplay 组件测试</h3>
    
    <div class="test-controls">
      <h4>测试控制</h4>
      <div class="control-row">
        <a-space>
          <span>事件ID:</span>
          <a-input 
            v-model:value="testEventId" 
            placeholder="输入事件ID" 
            style="width: 200px;" 
          />
          <a-button type="primary" @click="loadEventInfo">加载事件信息</a-button>
          <a-button @click="clearEventInfo">清空</a-button>
        </a-space>
      </div>
      
      <div class="preset-buttons">
        <span>预设事件ID:</span>
        <a-space>
          <a-button size="small" @click="setEventId('AT2025072804')">AT2025072804</a-button>
          <a-button size="small" @click="setEventId('AT2025072801')">AT2025072801</a-button>
          <a-button size="small" @click="setEventId('AT2025072701')">AT2025072701</a-button>
          <a-button size="small" @click="setEventId('REAL_TASK_001')">REAL_TASK_001</a-button>
        </a-space>
      </div>
    </div>

    <div class="test-result">
      <h4>测试结果</h4>
      <div class="result-container">
        <EventInfoDisplay 
          v-if="currentEventId"
          :event-id="currentEventId" 
        />
        <div v-else class="no-event">
          <a-empty description="请输入事件ID并点击加载按钮" />
        </div>
      </div>
    </div>

    <div class="test-log">
      <h4>测试日志</h4>
      <div class="log-container">
        <div v-for="(log, index) in testLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level" :class="log.level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import EventInfoDisplay from './EventInfoDisplay.vue'

const testEventId = ref('AT2025072804')
const currentEventId = ref('')
const testLogs = ref([])

function setEventId(eventId: string) {
  testEventId.value = eventId
  addLog('INFO', `设置事件ID: ${eventId}`)
}

function loadEventInfo() {
  if (!testEventId.value.trim()) {
    addLog('WARN', '请输入事件ID')
    return
  }
  
  currentEventId.value = testEventId.value
  addLog('INFO', `开始加载事件信息: ${testEventId.value}`)
}

function clearEventInfo() {
  currentEventId.value = ''
  testEventId.value = ''
  addLog('INFO', '清空事件信息')
}

function addLog(level: string, message: string) {
  const now = new Date()
  const time = now.toLocaleTimeString()
  testLogs.value.unshift({
    time,
    level,
    message
  })
  
  // 保持最多20条日志
  if (testLogs.value.length > 20) {
    testLogs.value = testLogs.value.slice(0, 20)
  }
}

// 初始化日志
addLog('INFO', 'EventInfoDisplay测试组件已加载')
</script>

<style scoped>
.event-info-test {
  padding: 16px;
}

.test-controls {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.control-row {
  margin-bottom: 12px;
}

.preset-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-result {
  margin-bottom: 16px;
}

.result-container {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  min-height: 300px;
}

.no-event {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.test-log {
  margin-top: 16px;
}

.log-container {
  background: #000;
  color: #fff;
  padding: 12px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 4px;
  display: flex;
  gap: 8px;
}

.log-time {
  color: #888;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-level.INFO {
  color: #52c41a;
}

.log-level.WARN {
  color: #faad14;
}

.log-level.ERROR {
  color: #f5222d;
}

.log-message {
  flex: 1;
}

h3 {
  color: #1890ff;
  margin-bottom: 16px;
}

h4 {
  color: #52c41a;
  margin-bottom: 8px;
}
</style>
