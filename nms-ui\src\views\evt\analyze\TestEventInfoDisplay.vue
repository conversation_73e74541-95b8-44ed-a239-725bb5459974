<template>
  <div class="test-event-info-display">
    <h2>EventInfoDisplay 组件测试页面</h2>
    
    <div class="test-section">
      <h3>测试1: 使用真实事件ID</h3>
      <div class="test-case">
        <h4>事件ID: AT2025072804</h4>
        <EventInfoDisplay :event-id="'AT2025072804'" />
      </div>
    </div>

    <div class="test-section">
      <h3>测试2: 使用另一个事件ID</h3>
      <div class="test-case">
        <h4>事件ID: AT2025072801</h4>
        <EventInfoDisplay :event-id="'AT2025072801'" />
      </div>
    </div>

    <div class="test-section">
      <h3>测试3: 使用不存在的事件ID</h3>
      <div class="test-case">
        <h4>事件ID: NONEXISTENT_ID</h4>
        <EventInfoDisplay :event-id="'NONEXISTENT_ID'" />
      </div>
    </div>

    <div class="test-section">
      <h3>测试4: 不提供事件ID</h3>
      <div class="test-case">
        <h4>无事件ID</h4>
        <EventInfoDisplay />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import EventInfoDisplay from './components/EventInfoDisplay.vue'
</script>

<style scoped>
.test-event-info-display {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
}

.test-case {
  margin-top: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

h2 {
  color: #1890ff;
  margin-bottom: 24px;
}

h3 {
  color: #52c41a;
  margin-bottom: 16px;
}

h4 {
  color: #722ed1;
  margin-bottom: 12px;
}
</style>
