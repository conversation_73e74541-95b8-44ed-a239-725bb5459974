<!--
  事件信息显示组件
  
  基于EventReportPage.vue的组件设计模式，提供事件基础信息的标准化显示
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-02-14
-->

<template>
  <div class="event-info-display">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
      <p class="loading-text">加载事件信息中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <a-result
        status="error"
        title="加载失败"
        :sub-title="error"
      >
        <template #extra>
          <a-button type="primary" @click="handleRetry">
            重新加载
          </a-button>
        </template>
      </a-result>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!eventInfo" class="empty-container">
      <a-empty description="暂无事件信息" />
    </div>

    <!-- 正常显示 -->
    <div v-else class="event-info-content">
      <!-- 滚动容器 -->
      <div class="scrollable-content">
        <!-- 事件核心信息 -->
        <a-card title="事件核心信息" class="info-card medical-theme-card" size="small">
          <template #extra>
            <a-tag
              :color="getStatusColor(eventInfo.status)"
              class="status-tag"
            >
              {{ getStatusText(eventInfo.status) }}
            </a-tag>
          </template>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="事件编号">
              <span class="event-code">{{ eventInfo.id || '-' }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="事件名称">
              {{ eventInfo.evtName || eventInfo.name || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="发生时间">
              {{ formatDateTime(eventInfo.evtTime || eventInfo.occurTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="上报时间">
              {{ formatDateTime(eventInfo.reportTime || eventInfo.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="发生科室">
              {{ eventInfo.deptName || eventInfo.department || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="上报人">
              {{ eventInfo.reporterName || eventInfo.reporter || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 事件等级与伤害程度 -->
        <a-card title="事件等级与伤害程度" class="info-card medical-theme-card" size="small">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="事件等级">
              <a-tag 
                v-if="eventInfo.evtLevel || eventInfo.level" 
                :color="getLevelColor(eventInfo.evtLevel || eventInfo.level)"
              >
                {{ eventInfo.evtLevel || eventInfo.level }}
              </a-tag>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="伤害程度">
              <a-tag 
                v-if="eventInfo.evtCritical || eventInfo.critical || eventInfo.injuryLevel" 
                :color="getCriticalColor(eventInfo.evtCritical || eventInfo.critical || eventInfo.injuryLevel)"
              >
                {{ eventInfo.evtCritical || eventInfo.critical || eventInfo.injuryLevel }}
              </a-tag>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 事件概述 -->
        <a-card title="事件概述" class="info-card medical-theme-card" size="small">
          <div class="event-summary-section">
            <div class="summary-content">
              {{ eventInfo.experience || eventInfo.summary || eventInfo.description || '暂无事件概述信息' }}
            </div>
          </div>
        </a-card>

        <!-- 事件详细信息 -->
        <a-card class="info-card medical-theme-card" size="small">
          <template #title>
            <div class="card-title-with-controls">
              <div class="title-text">
                <FileTextOutlined />
                事件详细信息
              </div>
              <div class="title-controls">
                <a-switch 
                  v-model:checked="showOnlyFilledFields"
                  size="small"
                  :checked-children="'已填'"
                  :un-checked-children="'全部'"
                />
                <a-tag color="blue">{{ filledFieldsCount }}/{{ filteredDynamicFields.length }}</a-tag>
              </div>
            </div>
          </template>
          
          <div v-if="filteredDynamicFields && filteredDynamicFields.length > 0" class="dynamic-fields-section">
            <div class="dynamic-fields-grid">
              <div 
                v-for="field in filteredDynamicFields" 
                :key="field.key"
                class="dynamic-field-item"
                :class="{ 'empty-field': !field.value || field.value === '-' }"
              >
                <div class="field-label">
                  {{ field.label }}
                  <CheckCircleOutlined v-if="field.value && field.value !== '-'" style="color: #52c41a; margin-left: 4px;" />
                  <MinusCircleOutlined v-else style="color: #d9d9d9; margin-left: 4px;" />
                  <span v-if="!field.value || field.value === '-'" class="empty-indicator">（未填写）</span>
                  <a-tag size="small" :color="getFieldTypeColor(field.type)" style="margin-left: 8px;">
                    {{ getFieldTypeLabel(field.type) }}
                  </a-tag>
                </div>
                <div class="field-value">
                  <div class="text-field-value" :class="{ 'empty-value': !field.value || field.value === '-' }">
                    <a-typography-paragraph 
                      v-if="isLongText(field.value)" 
                      :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
                      :content="getFieldDisplayValue(field)"
                    />
                    <span v-else>{{ getFieldDisplayValue(field) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="no-dynamic-fields">
            <a-empty :image="false" description="暂无专项信息字段" />
          </div>
        </a-card>

        <!-- 补充说明 -->
        <a-card title="补充说明" class="info-card medical-theme-card" size="small">
          <div class="additional-notes-section">
            <div class="notes-content">
              {{ eventInfo.sumnotes || eventInfo.notes || eventInfo.additionalNotes || '暂无补充说明信息' }}
            </div>
          </div>
        </a-card>

        <!-- 审核历史 -->
        <a-card title="审核历史" class="info-card medical-theme-card" size="small">
          <div v-if="eventInfo.auditHistory && eventInfo.auditHistory.length > 0">
            <div v-for="(audit, index) in eventInfo.auditHistory" :key="index" class="audit-item">
              <div class="audit-header">
                <span class="audit-action">
                  <AuditOutlined />
                  {{ audit.action }}
                </span>
                <span class="audit-time">{{ formatDateTime(audit.time) }}</span>
              </div>
              <div class="audit-details">
                <p><strong>审核人：</strong>{{ audit.auditor }}</p>
                <div v-if="audit.comment" class="audit-comment">
                  {{ audit.comment }}
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <a-empty description="暂无审核历史" :image="false" />
          </div>
        </a-card>

        <!-- 相关附件 -->
        <a-card title="相关附件" class="info-card medical-theme-card" size="small">
          <div class="attachments-section">
            <div v-if="eventInfo.attachments && eventInfo.attachments.length > 0" class="attachments-list">
              <div v-for="attachment in eventInfo.attachments" :key="attachment.id" class="attachment-item">
                <div class="attachment-info">
                  <FileOutlined class="attachment-icon" />
                  <div class="attachment-details">
                    <div class="attachment-name">{{ attachment.name }}</div>
                    <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                  </div>
                </div>
                <div class="attachment-actions">
                  <a-button size="small" @click="handlePreviewAttachment(attachment)">
                    预览
                  </a-button>
                  <a-button size="small" @click="handleDownloadAttachment(attachment)">
                    下载
                  </a-button>
                </div>
              </div>
            </div>
            <div v-else class="no-attachments">
              <a-empty description="暂无相关附件" :image="false" />
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 脚本内容将在下一步添加
</script>

<style scoped lang="less">
.event-info-display {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
