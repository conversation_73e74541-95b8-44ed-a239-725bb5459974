<template>
  <div>
    <BasicTable @register="registerTable">
      <!-- 工具栏 -->
      <template #toolbar>
        <a-button type="primary" v-auth="['evt:analyze:create']" :preIcon="IconEnum.ADD" @click="handleCreate">
          新增分析任务
        </a-button>
        <a-button :preIcon="IconEnum.TOOL" @click="handleOpenAnalysisTools">
          分析工具
        </a-button>
        <a-button v-auth="['evt:analyze:assign']" :preIcon="IconEnum.AUTH" @click="handleBatchAssign">
          批量分配
        </a-button>
      </template>

      <!-- 表格操作列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: IconEnum.EDIT,
                label: '分析',
                auth: 'evt:analyze:update',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: IconEnum.VIEW,
                label: '查看',
                auth: 'evt:analyze:query',
                onClick: handleView.bind(null, record),
              },
              {
                icon: IconEnum.AUTH,
                label: '分配',
                auth: 'evt:analyze:assign',
                ifShow: () => record.status === 'pending',
                onClick: handleAssign.bind(null, record),
              },
              {
                icon: IconEnum.DELETE,
                danger: true,
                label: '删除',
                auth: 'evt:analyze:delete',
                popConfirm: {
                  title: '确定要删除这条记录吗？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>

    <!-- 分配任务弹窗 -->
    <!-- <AssignTaskModal @register="registerAssignModal" @success="reload" /> -->
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { BasicTable, useTable, TableAction } from '@/components/Table'
import { useMessage } from '@/hooks/web/useMessage'
import { IconEnum } from '@/enums/appEnum'

// import AssignTaskModal from './components/AssignTaskModal.vue'
import { columns, searchFormSchema } from './analyze.data'
import {
  getAnalyzeTaskPage,
  deleteAnalyzeTask,
} from '@/api/evt/analyze'

defineOptions({ name: 'EvtAnalyzeIndex' })

const router = useRouter()
const { createMessage } = useMessage()

// 表格配置
const [registerTable, { reload, getSelectRows }] = useTable({
  title: '事件分析任务列表',
  api: getAnalyzeTaskPage,
  rowKey: 'id',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 240,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

// 分配任务弹窗
// const [registerAssignModal, { openModal: openAssignModal }] = useModal()

// 新增分析任务
function handleCreate() {
  router.push('/evt/analyze/create')
}

// 打开分析工具
function handleOpenAnalysisTools() {
  router.push('/evt/analyze/tools')
}

// 编辑分析任务
function handleEdit(record: Recordable) {
  // 使用事件ID而不是任务ID进行导航，确保数据一致性
  const eventId = record.eventId || record.id
  router.push(`/evt/analyze/detail/${eventId}?mode=edit&taskId=${record.id}`)
}

// 查看分析任务
function handleView(record: Recordable) {
  // 使用事件ID而不是任务ID进行导航，确保数据一致性
  const eventId = record.eventId || record.id
  router.push(`/evt/analyze/detail/${eventId}?mode=view&taskId=${record.id}`)
}

// 分配任务
function handleAssign(record: Recordable) {
  // openAssignModal(true, { record })
  createMessage.info('分配功能开发中...')
}

// 批量分配
function handleBatchAssign() {
  const rows = getSelectRows()
  if (rows.length === 0) {
    createMessage.warning('请选择要分配的任务')
    return
  }
  // openAssignModal(true, { records: rows })
  createMessage.info('批量分配功能开发中...')
}

// 删除任务
async function handleDelete(record: Recordable) {
  try {
    await deleteAnalyzeTask(record.id)
    createMessage.success('删除成功')
    reload()
  } catch (error) {
    createMessage.error('删除失败')
  }
}

// 状态和优先级渲染已移至 analyze.data.ts 中的 customRender 函数
</script>
