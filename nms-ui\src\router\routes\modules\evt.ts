import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';
import { t } from '@/hooks/web/useI18n';

const evt: AppRouteModule = {
  path: '/evt',
  name: 'Evt',
  component: LAYOUT,
  redirect: '/evt/info',
  meta: {
    orderNo: 30,
    icon: 'ion:alert-circle-outline',
    title: t('routes.evt.moduleName'),
  },
  children: [
    // 事件上报管理
    {
      path: 'info',
      name: 'EvtInfo',
      component: () => import('@/views/evt/info/index.vue'),
      meta: {
        title: t('routes.evt.info'),
        icon: 'ion:document-text-outline',
      },
    },

    // 新增事件上报（页面式）
    {
      path: 'info/create',
      name: 'EvtInfoCreate',
      component: () => import('@/views/evt/info/EventReportPage.vue'),
      meta: {
        title: t('routes.evt.infoCreate'),
        icon: 'ion:add-circle-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/info',
      },
    },

    // 编辑事件上报
    {
      path: 'info/edit/:id',
      name: 'EvtInfoEdit',
      component: () => import('@/views/evt/info/EventReportPage.vue'),
      meta: {
        title: t('routes.evt.infoEdit'),
        icon: 'ion:create-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/info',
      },
    },

    // 测试动态验证页面
    {
      path: 'info/test-validation',
      name: 'EvtTestValidation',
      component: () => import('@/views/evt/info/TestDynamicValidation.vue'),
      meta: {
        title: '动态字段验证测试',
        icon: 'ion:flask-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/info',
      },
    },

    // 查看事件详情
    {
      path: 'info/detail/:id',
      name: 'EvtInfoDetail',
      component: () => import('@/views/evt/info/EventReportPage.vue'),
      meta: {
        title: t('routes.evt.infoDetail'),
        icon: 'ion:eye-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/info',
      },
    },

    // 从草稿继续编辑
    {
      path: 'info/draft/:draftId',
      name: 'EvtInfoFromDraft',
      component: () => import('@/views/evt/info/EventReportPage.vue'),
      meta: {
        title: t('routes.evt.infoDraft'),
        icon: 'ion:document-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/info',
      },
    },

    // 通用事件上报页面（支持查看、编辑、新增模式）
    {
      path: 'info/report',
      name: 'EvtInfoReport',
      component: () => import('@/views/evt/info/EventReportPage.vue'),
      meta: {
        title: t('routes.evt.infoReport'),
        icon: 'ion:document-text-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/info',
      },
    },

    // ==================== 动态事件类型路由系统 v2.0 ====================
    //
    // 🎯 基于现有system_menu表字段的智能路由方案
    // 完全零数据库修改，向后兼容，支持多种配置方式
    //
    // 📋 支持的配置方式：
    // 1. 路径后缀方式（推荐）：/evt/info/info_1, /evt/info/info_2 等
    // 2. Query参数方式：/evt/info?eventType=2&eventTypeName=护理事件
    // 3. 权限标识方式：permission: "evt:info:create:2:护理事件"
    // 4. 路由Meta方式：meta中包含eventType和eventTypeName
    //
    // 🔧 标准菜单配置示例：
    // INSERT INTO system_menu (
    //   name, permission, type, sort, parent_id,
    //   path, icon, component, status, component_name
    // ) VALUES (
    //   '护理事件上报', 'evt:info:create:2:护理事件', 1, 10, [父菜单ID],
    //   '/evt/info/info_1', 'ion:medical-outline', 'evt/info/EventReportPage', 0, 'EventReportPage'
    // );
    //
    // 🎨 事件类型与路径后缀映射：
    // - info_1 → 护理事件 (eventType: '2')
    // - info_2 → 医疗事件 (eventType: '1')
    // - info_3 → 药品事件 (eventType: '1')
    // - info_4 → 设备事件 (eventType: '3')
    // - info_5 → 手术事件 (eventType: '1')
    // - info_6 → 其他医疗事件 (eventType: '1')
    //
    // 🔍 多层级解析优先级：
    // 1. 路径后缀解析（最高优先级）
    // 2. 权限标识解析
    // 3. 路由Meta解析
    // 4. Query参数解析
    // 5. 路由参数解析
    // 6. 传统路由名称解析（向后兼容）
    //
    // ✅ 技术优势：
    // - 完全基于现有system_menu表字段，零数据库修改
    // - 智能多源参数解析，灵活配置
    // - 向后兼容现有硬编码路由
    // - 支持动态添加新的事件类型入口
    // - 便于维护和扩展
    // - 保持路径命名规范一致性
    //
    // 📖 详细配置指南：docs/菜单配置管理/事件类型路由配置指南.md

    // ==================== 事件分析管理模块 ====================
    {
      path: 'analyze',
      name: 'EvtAnalyze',
      component: () => import('@/views/evt/analyze/index.vue'),
      meta: {
        title: '事件分析管理',
        icon: 'ion:analytics-outline',
        // roles: ['quality_specialist', 'analysis_expert', 'quality_director'],
      },
    },

    // 新增分析任务页面 (静态路由，必须在动态路由之前)
    {
      path: 'analyze/create',
      name: 'EvtAnalyzeCreate',
      component: () => import('@/views/evt/analyze/CreatePage.vue'),
      meta: {
        title: '新增分析任务',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        // roles: ['quality_specialist', 'analysis_expert', 'quality_director'],
      },
    },

    // 分析工具页面 (静态路由)
    {
      path: 'analyze/tools',
      name: 'EvtAnalyzeTools',
      component: () => import('@/views/evt/analyze/ToolsPage.vue'),
      meta: {
        title: '分析工具',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        ignoreAuth: true, // 绕过权限检查
        // roles: ['quality_specialist', 'analysis_expert', 'quality_director'],
      },
    },

    // EventInfoDisplay 测试页面 (静态路由)
    {
      path: 'analyze/test-event-info',
      name: 'EvtAnalyzeTestEventInfo',
      component: () => import('@/views/evt/analyze/TestEventInfoDisplay.vue'),
      meta: {
        title: 'EventInfoDisplay测试',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        ignoreAuth: true, // 绕过权限检查
      },
    },

    // EventAnalyzePage 直接测试页面 (静态路由)
    {
      path: 'analyze/direct-test',
      name: 'EvtAnalyzeDirectTest',
      component: () => import('@/views/evt/analyze/DirectTestPage.vue'),
      meta: {
        title: 'EventAnalyzePage直接测试',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        ignoreAuth: true, // 绕过权限检查
      },
    },

    // 分析导出页面 (静态路由)
    {
      path: 'analyze/export',
      name: 'EvtAnalyzeExport',
      component: () => import('@/views/evt/analyze/ExportPage.vue'),
      meta: {
        title: '分析导出',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        // roles: ['quality_specialist', 'analysis_expert', 'quality_director'],
      },
    },

    // 事件分析详情页面 (动态路由，放在静态路由之后)
    {
      path: 'analyze/detail/:id',
      name: 'EvtAnalyzeDetail',
      component: () => import('@/views/evt/analyze/EventAnalyzePage.vue'),
      meta: {
        title: '事件分析详情',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        // roles: ['quality_specialist', 'analysis_expert', 'quality_director'],
      },
    },

    // 事件分析详情页面 (带模式参数的动态路由)
    {
      path: 'analyze/detail/:id/:mode',
      name: 'EvtAnalyzeDetailWithMode',
      component: () => import('@/views/evt/analyze/EventAnalyzePage.vue'),
      meta: {
        title: '事件分析详情',
        hideMenu: true,
        currentActiveMenu: '/evt/analyze',
        // roles: ['quality_specialist', 'analysis_expert', 'quality_director'],
      },
    },

    // 兼容旧路由格式 (向后兼容)
    {
      path: 'analyze/:id',
      name: 'EvtAnalyzeDetailLegacy',
      redirect: (to) => {
        return `/evt/analyze/detail/${to.params.id}`
      },
    },

    // ==================== 事件改进管理模块 ====================
    {
      path: 'improve',
      name: 'EvtImprove',
      component: () => import('@/views/evt/improve/index.vue'),
      meta: {
        title: '事件改进管理',
        icon: 'ion:construct-outline',
        roles: ['improvement_specialist', 'quality_specialist', 'quality_director'],
      },
    },

    // 事件改进详情页面
    {
      path: 'improve/:id',
      name: 'EvtImproveDetail',
      component: () => import('@/views/evt/improve/EventImprovePage.vue'),
      meta: {
        title: '事件改进详情',
        hideMenu: true,
        currentActiveMenu: '/evt/improve',
        roles: ['improvement_specialist', 'quality_specialist', 'quality_director'],
      },
    },

    // ==================== 知识库管理模块（统一架构） ====================
    {
      path: 'knowledge',
      name: 'EvtKnowledge',
      component: () => import('@/views/evt/knowledge/index.vue'),
      meta: {
        title: '知识库管理',
        icon: 'ion:library-outline',
        roles: ['quality_specialist', 'knowledge_manager', 'quality_director'],
      },
    },

    // 知识浏览（独立页面模式，支持深度链接）
    {
      path: 'knowledge/browse',
      name: 'EvtKnowledgeBrowse',
      component: () => import('@/views/evt/knowledge/browse/index.vue'),
      meta: {
        title: '知识浏览',
        icon: 'ion:search-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/knowledge',
      },
    },

    // 知识管理（独立页面模式）
    {
      path: 'knowledge/manage',
      name: 'EvtKnowledgeManage',
      component: () => import('@/views/evt/knowledge/manage/index.vue'),
      meta: {
        title: '知识管理',
        icon: 'ion:create-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/knowledge',
        roles: ['knowledge_manager', 'quality_director'],
      },
    },

    // 使用统计（独立页面模式）
    {
      path: 'knowledge/stats',
      name: 'EvtKnowledgeStats',
      component: () => import('@/views/evt/knowledge/stats/index.vue'),
      meta: {
        title: '使用统计',
        icon: 'ion:bar-chart-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/knowledge',
        roles: ['quality_specialist', 'knowledge_manager', 'quality_director'],
      },
    },

    // 知识详情页面
    {
      path: 'knowledge/detail/:id',
      name: 'EvtKnowledgeDetail',
      component: () => import('@/views/evt/knowledge/detail/index.vue'),
      meta: {
        title: '知识详情',
        icon: 'ion:eye-outline',
        hideMenu: true,
        currentActiveMenu: '/evt/knowledge',
      },
    },

    // 系统配置管理
    {
      path: 'sys',
      name: 'EvtSys',
      redirect: '/evt/sys/sysevtcontent',
      meta: {
        title: t('routes.evt.sys'),
        icon: 'ion:settings-outline',
      },
      children: [
        {
          path: 'sysevtcontent',
          name: 'SysEvtcontent',
          component: () => import('@/views/evt/sys/sysevtcontent/index.vue'),
          meta: {
            title: t('routes.evt.sysevtcontent'),
            icon: 'ion:document-text-outline',
          },
        },
      ],
    },

    // {{ AURA-X: Fix - 恢复事件审核路由，解决404问题. Approval: 寸止(ID:1720089900). }}
    // 事件审核管理
    {
      path: 'audit',
      name: 'EvtAudit',
      redirect: '/evt/audit/list',
      meta: {
        title: t('routes.evt.audit'),
        icon: 'ion:checkmark-circle-outline',
      },
      children: [
        {
          path: 'list',
          name: 'EvtAuditList',
          component: () => import('@/views/evt/audit/index.vue'),
          meta: {
            title: t('routes.evt.auditList'),
            icon: 'ion:list-outline',
          },
        },
        // {{ AURA-X: Fix - 临时注释不存在的审核人员管理路由. Approval: 寸止(ID:1720101300). }}
        // {
        //   path: 'person',
        //   name: 'EvtAuditPerson',
        //   component: () => import('@/views/evt/audit/person/index.vue'),
        //   meta: {
        //     title: t('routes.evt.auditPerson'),
        //     icon: 'ion:person-outline',
        //   },
        // },
      ],
    },

    // 事件分析管理
    // {
    //   path: 'analysis',
    //   name: 'EvtAnalysis',
    //   redirect: '/evt/analysis/cause',
    //   meta: {
    //     title: t('routes.evt.analysis'),
    //     icon: 'ion:analytics-outline',
    //   },
    //   children: [
    //     {
    //       path: 'cause',
    //       name: 'EvtCause',
    //       component: () => import('@/views/evt/analysis/cause/index.vue'),
    //       meta: {
    //         title: t('routes.evt.cause'),
    //         icon: 'ion:git-network-outline',
    //       },
    //     },
    //     {
    //       path: 'measures',
    //       name: 'EvtMeasures',
    //       component: () => import('@/views/evt/analysis/measures/index.vue'),
    //       meta: {
    //         title: t('routes.evt.measures'),
    //         icon: 'ion:construct-outline',
    //       },
    //     },
    //     {
    //       path: 'assignment',
    //       name: 'EvtAssignment',
    //       component: () => import('@/views/evt/analysis/assignment/index.vue'),
    //       meta: {
    //         title: t('routes.evt.assignment'),
    //         icon: 'ion:person-add-outline',
    //       },
    //     },
    //   ],
    // },

    // 统计分析
    // {
    //   path: 'statistics',
    //   name: 'EvtStatistics',
    //   redirect: '/evt/statistics/dashboard',
    //   meta: {
    //     title: t('routes.evt.statistics'),
    //     icon: 'ion:bar-chart-outline',
    //   },
    //   children: [
    //     {
    //       path: 'dashboard',
    //       name: 'EvtStatisticsDashboard',
    //       component: () => import('@/views/evt/statistics/dashboard/index.vue'),
    //       meta: {
    //         title: t('routes.evt.statisticsDashboard'),
    //         icon: 'ion:speedometer-outline',
    //       },
    //     },
    //     {
    //       path: 'report',
    //       name: 'EvtStatisticsReport',
    //       component: () => import('@/views/evt/statistics/report/index.vue'),
    //       meta: {
    //         title: t('routes.evt.statisticsReport'),
    //         icon: 'ion:document-outline',
    //       },
    //     },
    //     {
    //       path: 'trend',
    //       name: 'EvtStatisticsTrend',
    //       component: () => import('@/views/evt/statistics/trend/index.vue'),
    //       meta: {
    //         title: t('routes.evt.statisticsTrend'),
    //         icon: 'ion:trending-up-outline',
    //       },
    //     },
    //   ],
    // },

    // ==================== 临时haem路径支持 ====================
    // 临时解决方案：在evt模块中添加haem路径支持
    // 这样可以立即修复 /haem/report/info_X 路由404问题
    // 正常情况下应该重启开发服务器来加载独立的haem.ts模块

    // 护理事件上报 - haem路径
    {
      path: '/haem/report/info_1',
      name: 'HaemInfo_1',
      component: () => import('@/views/evt/info/index.vue'),
      meta: {
        title: '护理事件上报',
        icon: 'ion:medical-outline',
        eventType: '1',
        eventTypeName: '护理事件',
        hideMenu: true,
      },
    },

    // 医疗事件上报 - haem路径
    {
      path: '/haem/report/info_2',
      name: 'HaemInfo_2',
      component: () => import('@/views/evt/info/index.vue'),
      meta: {
        title: '医疗事件上报',
        icon: 'ion:medical-outline',
        eventType: '2',
        eventTypeName: '医疗事件',
        hideMenu: true,
      },
    },

    // 行政事件上报 - haem路径
    {
      path: '/haem/report/info_3',
      name: 'HaemInfo_3',
      component: () => import('@/views/evt/info/index.vue'),
      meta: {
        title: '行政事件上报',
        icon: 'ion:briefcase-outline',
        eventType: '3',
        eventTypeName: '行政事件',
        hideMenu: true,
      },
    },

    // 后勤事件上报 - haem路径
    {
      path: '/haem/report/info_4',
      name: 'HaemInfo_4',
      component: () => import('@/views/evt/info/index.vue'),
      meta: {
        title: '后勤事件上报',
        icon: 'ion:construct-outline',
        eventType: '4',
        eventTypeName: '后勤事件',
        hideMenu: true,
      },
    },
  ],
};

export default evt;