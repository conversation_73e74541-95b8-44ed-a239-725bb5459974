<template>
  <div class="direct-test-page">
    <h1>EventAnalyzePage 直接测试</h1>
    
    <div class="test-controls">
      <h3>测试控制</h3>
      <div class="control-group">
        <label>事件ID:</label>
        <a-input v-model:value="testEventId" placeholder="输入事件ID" style="width: 200px; margin-right: 10px;" />
        <a-button type="primary" @click="loadTestPage">加载测试页面</a-button>
      </div>
      
      <div class="preset-buttons">
        <h4>预设事件ID:</h4>
        <a-button @click="setEventId('AT2025072804')" style="margin-right: 8px;">AT2025072804</a-button>
        <a-button @click="setEventId('AT2025072801')" style="margin-right: 8px;">AT2025072801</a-button>
        <a-button @click="setEventId('AT2025072701')" style="margin-right: 8px;">AT2025072701</a-button>
      </div>
    </div>

    <div v-if="showTestPage" class="test-content">
      <h3>EventAnalyzePage 组件测试 (事件ID: {{ currentEventId }})</h3>
      <div class="component-wrapper">
        <EventAnalyzePage />
      </div>
    </div>

    <div v-if="!showTestPage" class="instructions">
      <h3>使用说明</h3>
      <p>1. 输入一个事件ID或点击预设按钮</p>
      <p>2. 点击"加载测试页面"按钮</p>
      <p>3. 查看EventAnalyzePage组件的渲染结果</p>
      <p>4. 特别关注"事件信息"标签页是否正确显示事件详情</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import EventAnalyzePage from './EventAnalyzePage.vue'

const route = useRoute()
const router = useRouter()

const testEventId = ref('AT2025072804')
const currentEventId = ref('')
const showTestPage = ref(false)

function setEventId(eventId: string) {
  testEventId.value = eventId
}

function loadTestPage() {
  if (!testEventId.value.trim()) {
    return
  }
  
  currentEventId.value = testEventId.value
  showTestPage.value = true
  
  // 模拟路由参数
  router.push({
    path: '/evt/analyze/direct-test',
    query: { id: testEventId.value }
  })
}

// 如果URL中有id参数，自动加载
if (route.query.id) {
  testEventId.value = route.query.id as string
  currentEventId.value = testEventId.value
  showTestPage.value = true
}
</script>

<style scoped>
.direct-test-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.control-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.control-group label {
  margin-right: 10px;
  font-weight: bold;
}

.preset-buttons {
  margin-top: 16px;
}

.preset-buttons h4 {
  margin-bottom: 8px;
}

.test-content {
  border: 2px solid #1890ff;
  border-radius: 8px;
  padding: 16px;
}

.component-wrapper {
  margin-top: 16px;
  min-height: 600px;
}

.instructions {
  background: #e6f7ff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #91d5ff;
}

.instructions h3 {
  color: #1890ff;
  margin-bottom: 16px;
}

.instructions p {
  margin-bottom: 8px;
  color: #666;
}

h1 {
  color: #1890ff;
  margin-bottom: 24px;
}

h3 {
  color: #52c41a;
  margin-bottom: 16px;
}
</style>
