<!--
  分析工具页面
  
  提供独立的分析工具访问页面，支持直接使用各种分析工具
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-01-25
-->

<template>
  <div class="analyze-tools-page">
    <!-- 页面头部 -->
    <PageWrapper 
      title="分析工具" 
      content-full-height
      fixed-height
    >
      <!-- 头部操作区 -->
      <template #headerContent>
        <div class="header-actions">
          <a-space>
            <a-button @click="handleBack">
              <template #icon><ArrowLeftOutlined /></template>
              返回分析管理
            </a-button>
          </a-space>
        </div>
      </template>

      <!-- 工具选择面板 -->
      <div class="tools-container">
        <a-row :gutter="[24, 24]">
          <a-col 
            v-for="tool in availableTools" 
            :key="tool.id"
            :xs="24" :sm="12" :md="8" :lg="6"
          >
            <a-card 
              :title="tool.name"
              hoverable
              class="tool-card"
              @click="handleToolSelect(tool)"
            >
              <template #cover>
                <div class="tool-icon">
                  <component :is="tool.icon" :style="{ fontSize: '48px', color: tool.color }" />
                </div>
              </template>
              
              <div class="tool-description">
                {{ tool.description }}
              </div>
              
              <template #actions>
                <a-button type="primary" block @click="handleToolSelect(tool)">
                  使用工具
                </a-button>
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 工具使用区域 -->
      <div v-if="selectedTool" class="tool-workspace">
        <a-card :title="selectedTool.name" class="workspace-card">
          <template #extra>
            <a-button @click="handleCloseWorkspace">
              <template #icon><CloseOutlined /></template>
              关闭
            </a-button>
          </template>
          
          <!-- 动态加载工具组件 -->
          <component 
            :is="selectedTool.component"
            v-bind="selectedTool.props"
            @save="handleToolSave"
            @export="handleToolExport"
          />
        </a-card>
      </div>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { PageWrapper } from '@/components/Page'
import {
  ArrowLeftOutlined,
  CloseOutlined,
  ShareAltOutlined,
  QuestionCircleOutlined,
  BarChartOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

// 导入分析工具组件
import FishboneChart from './components/FishboneChart.vue'
import FiveWhyAnalysis from './components/FiveWhyAnalysis.vue'
import PDCAAnalysis from './components/PDCAAnalysis.vue'
import FMEAAnalysis from './components/FMEAAnalysis.vue'
import DecisionTree from './components/DecisionTree.vue'
import EventInfoTest from './components/EventInfoTest.vue'

const router = useRouter()

// 可用的分析工具
const availableTools = ref([
  {
    id: 'fishbone',
    name: '鱼骨图分析',
    description: '系统性地分析问题的各种可能原因，通过图形化方式展示因果关系',
    icon: ShareAltOutlined,
    color: '#1890ff',
    component: FishboneChart,
    props: { readonly: false }
  },
  {
    id: 'five-why',
    name: '5Why分析',
    description: '通过连续5次询问"为什么"来深入挖掘问题的根本原因',
    icon: QuestionCircleOutlined,
    color: '#52c41a',
    component: FiveWhyAnalysis,
    props: { readonly: false }
  },
  {
    id: 'pdca',
    name: 'PDCA循环',
    description: '计划-执行-检查-行动的持续改进循环管理工具',
    icon: BarChartOutlined,
    color: '#faad14',
    component: PDCAAnalysis,
    props: { readonly: false }
  },
  {
    id: 'fmea',
    name: 'FMEA分析',
    description: '失效模式与影响分析，预防性地识别和评估潜在失效模式',
    icon: NodeIndexOutlined,
    color: '#f5222d',
    component: FMEAAnalysis,
    props: { readonly: false }
  },
  {
    id: 'decision-tree',
    name: '决策树分析',
    description: '通过树状图形展示决策过程和各种可能的结果路径',
    icon: BranchesOutlined,
    color: '#722ed1',
    component: DecisionTree,
    props: { readonly: false }
  },
  {
    id: 'event-info-test',
    name: 'EventInfoDisplay测试',
    description: '测试EventInfoDisplay组件的事件信息显示功能',
    icon: InfoCircleOutlined,
    color: '#1890ff',
    component: EventInfoTest,
    props: { readonly: false }
  }
])

// 当前选中的工具
const selectedTool = ref(null)

// 处理返回
const handleBack = () => {
  router.push('/evt/analyze')
}

// 处理工具选择
const handleToolSelect = (tool) => {
  selectedTool.value = tool
}

// 关闭工作区
const handleCloseWorkspace = () => {
  selectedTool.value = null
}

// 处理工具保存
const handleToolSave = (data) => {
  console.log('工具数据保存:', data)
  // TODO: 实现保存逻辑
}

// 处理工具导出
const handleToolExport = (data) => {
  console.log('工具数据导出:', data)
  // TODO: 实现导出逻辑
}
</script>

<style scoped lang="less">
.analyze-tools-page {
  height: 100%;
  
  .header-actions {
    display: flex;
    align-items: center;
  }
  
  .tools-container {
    margin-bottom: 24px;
    
    .tool-card {
      height: 100%;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
      
      .tool-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 120px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      
      .tool-description {
        color: #666;
        line-height: 1.6;
        min-height: 48px;
      }
    }
  }
  
  .tool-workspace {
    .workspace-card {
      min-height: 500px;
    }
  }
}
</style>
