<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态字段测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #e8e8e8;
            padding-bottom: 16px;
            margin-bottom: 24px;
        }
        .title {
            font-size: 24px;
            color: #1890ff;
            margin: 0 0 8px 0;
        }
        .description {
            color: #666;
            margin: 0;
        }
        .demo-section {
            margin-bottom: 32px;
        }
        .demo-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 16px;
            padding-left: 12px;
            border-left: 4px solid #1890ff;
        }
        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
        }
        .field-item {
            padding: 0;
            border: 1px solid #d9e6f2;
            border-radius: 4px;
            background-color: #ffffff;
            overflow: hidden;
        }
        .field-label {
            background: linear-gradient(135deg, #f0f5ff 0%, #f8fbff 100%);
            padding: 8px 12px;
            border-bottom: 1px solid #d9e6f2;
            color: #1890ff;
            font-size: 13px;
            font-weight: 600;
            margin: 0;
        }
        .empty-indicator {
            color: #999;
            font-weight: 400;
            font-size: 12px;
        }
        .field-value {
            background-color: #fafafa;
            padding: 12px;
            color: #262626;
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
        }
        .text-field-value {
            word-break: break-word;
            line-height: 1.5;
        }
        .empty-value {
            color: #999;
            font-style: italic;
        }
        .controls {
            margin-bottom: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        .switch {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .switch input {
            width: 40px;
            height: 20px;
        }
        .counter {
            background: #1890ff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">EventInfoDisplay 动态字段优化测试</h1>
            <p class="description">测试动态字段显示逻辑，包括过滤开关功能和空值字段显示</p>
        </div>

        <div class="controls">
            <div class="switch">
                <label for="filterSwitch">显示模式：</label>
                <input type="checkbox" id="filterSwitch" onchange="toggleFilter()">
                <span id="switchLabel">显示全部字段</span>
            </div>
            <div class="counter" id="counter">8/8</div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">专项信息字段</h2>
            <div class="field-grid" id="fieldGrid">
                <!-- 字段将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟动态字段数据
        const mockFields = [
            {
                key: 'evtLocationCategory',
                label: '发生地点类别',
                value: '高危服区域(手术、介入、分娩室与血液透析室等)',
                type: 'select',
                hasValue: true
            },
            {
                key: 'diagnosisCategory',
                label: '诊疗疾病类别',
                value: '损伤、中毒和外因的某些其他后果',
                type: 'select',
                hasValue: true
            },
            {
                key: 'evtSeverity',
                label: '事件严重程度',
                value: '',
                type: 'radio',
                hasValue: false
            },
            {
                key: 'evtCause',
                label: '事件原因分析',
                value: '',
                type: 'textarea',
                hasValue: false
            },
            {
                key: 'evtPreventive',
                label: '预防措施',
                value: '',
                type: 'textarea',
                hasValue: false
            },
            {
                key: 'evtWitness',
                label: '目击人员',
                value: '',
                type: 'text',
                hasValue: false
            },
            {
                key: 'evtEquipment',
                label: '相关设备',
                value: '',
                type: 'text',
                hasValue: false
            },
            {
                key: 'evtMedication',
                label: '相关药品',
                value: '',
                type: 'text',
                hasValue: false
            }
        ];

        let showOnlyFilled = false;

        function getFieldTypeLabel(type) {
            const typeMap = {
                'text': '文本',
                'textarea': '长文本',
                'select': '选择',
                'radio': '单选',
                'checkbox': '复选',
                'date': '日期',
                'number': '数字'
            };
            return typeMap[type] || type;
        }

        function renderFields() {
            const grid = document.getElementById('fieldGrid');
            const filteredFields = showOnlyFilled ?
                mockFields.filter(field => field.hasValue) :
                mockFields;

            grid.innerHTML = filteredFields.map(field => `
                <div class="field-item">
                    <div class="field-label">
                        ${field.label}
                        ${!field.hasValue && !showOnlyFilled ? '<span class="empty-indicator">（未填写）</span>' : ''}
                    </div>
                    <div class="field-value">
                        <span class="text-field-value ${field.hasValue ? '' : 'empty-value'}">
                            ${field.hasValue ? field.value : '-'}
                        </span>
                    </div>
                </div>
            `).join('');

            // 更新计数器
            const filledCount = mockFields.filter(f => f.hasValue).length;
            const totalCount = mockFields.length;
            const displayedCount = filteredFields.length;
            document.getElementById('counter').textContent = showOnlyFilled ?
                `${filledCount}/${totalCount}` :
                `${displayedCount}/${totalCount}`;
        }

        function toggleFilter() {
            const checkbox = document.getElementById('filterSwitch');
            const label = document.getElementById('switchLabel');
            
            showOnlyFilled = checkbox.checked;
            label.textContent = showOnlyFilled ? '仅显示已填写' : '显示全部字段';
            
            renderFields();
        }

        // 初始化页面
        renderFields();
    </script>
</body>
</html>
