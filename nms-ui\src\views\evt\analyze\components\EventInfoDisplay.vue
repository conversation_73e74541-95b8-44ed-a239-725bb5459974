<!--
  事件信息显示组件

  基于EventReportPage.vue的组件设计模式，提供事件基础信息的标准化显示

  <AUTHOR>
  @version 1.0.0
  @since 2025-02-14
-->

<template>
  <div class="event-info-display">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
      <p class="loading-text">加载事件信息中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <a-result
        status="error"
        title="加载失败"
        :sub-title="error"
      >
        <template #extra>
          <a-button type="primary" @click="handleRetry">
            重新加载
          </a-button>
        </template>
      </a-result>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!eventInfo" class="empty-container">
      <a-empty description="暂无事件信息" />
    </div>

    <!-- 正常显示 -->
    <div v-else class="event-info-content">
      <!-- 滚动容器 -->
      <div class="scrollable-content">
        <!-- 事件核心信息 -->
        <a-card title="事件核心信息" class="info-card medical-theme-card" size="small">
          <template #extra>
            <a-tag
              :color="getStatusColor(eventInfo.status)"
              class="status-tag"
            >
              {{ getStatusText(eventInfo.status) }}
            </a-tag>
          </template>

          <a-descriptions :column="2" size="small" class="medical-descriptions">
            <a-descriptions-item label="事件编号">
              <span class="event-code">{{ eventInfo.eventCode || eventInfo.id || '-' }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="事件名称">
              <span class="event-name">{{ eventInfo.eventNameText || eventInfo.eventName || '未知事件' }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="事件分类">
              <a-tag color="blue">{{ eventInfo.eventTypeName || eventInfo.eventType || '未知类型' }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="发生时间">
              <span class="event-time">{{ formatDateTime(eventInfo.occurTime || eventInfo.eventTime) }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="发生科室">
              <span class="event-dept">{{ eventInfo.deptName || eventInfo.department || '-' }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="事件级别">
              <a-tag :color="getLevelColor(eventInfo.level || eventInfo.evtLevel)" size="large">
                {{ getLevelText(eventInfo.level || eventInfo.evtLevel) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="伤害程度">
              <a-tag :color="getCriticalColor(eventInfo.evtCritical || eventInfo.injurySeverity)" size="large">
                {{ getCriticalText(eventInfo.evtCritical || eventInfo.injurySeverity) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>

          <a-divider />

          <div class="event-summary-section">
            <h4>事件概述</h4>
            <div class="summary-content">
              {{ eventInfo.experience || eventInfo.description || '暂无描述' }}
            </div>
          </div>

          <a-divider />

          <div class="additional-notes-section">
            <h4>补充说明</h4>
            <div class="notes-content">
              {{ eventInfo.sumnotes || eventInfo.additionalNotes || '暂无补充说明' }}
            </div>
          </div>
        </a-card>

        <!-- 事件详细信息 -->
        <a-card class="info-card medical-theme-card" :bordered="false">
          <template #title>
            <div class="card-title-with-controls">
              <span>事件详细信息</span>
              <a-space>
                <a-tooltip title="基于事件类型的专项信息">
                  <FileTextOutlined style="color: #1890ff;" />
                </a-tooltip>
                <a-switch
                  v-model:checked="showOnlyFilledFields"
                  size="small"
                  checked-children="已填"
                  un-checked-children="全部"
                />
                <a-tag v-if="dynamicFields.length > 0" color="blue" size="small">
                  {{ filledFieldsCount }}/{{ dynamicFields.length }}
                </a-tag>
              </a-space>
            </div>
          </template>

          <!-- 专项信息（动态字段）优先显示 -->
          <div class="dynamic-fields-section">
            <div v-if="filteredDynamicFields.length > 0" class="dynamic-fields-grid">
              <div
                v-for="field in filteredDynamicFields"
                :key="field.key"
                class="dynamic-field-item"
                :class="{
                  'table-field': field.isTable,
                  'empty-field': field.isEmpty,
                  'filled-field': field.hasValue
                }"
              >
                <div class="field-label">
                  {{ field.label }}
                  <span v-if="field.isEmpty && !showOnlyFilledFields" class="empty-indicator">（未填写）</span>
                </div>
                <div class="field-value">
                  <div v-if="field.isTable" v-html="field.value" class="table-field-container"></div>
                  <span v-else class="text-field-value" :class="{ 'empty-value': field.isEmpty }">
                    {{ getFieldDisplayValue(field) }}
                  </span>
                </div>
              </div>
            </div>

            <div v-else class="no-dynamic-fields">
              <a-empty :image="false" description="暂无专项信息字段" />
            </div>
          </div>
        </a-card>

        <!-- 当前处理状态 -->
        <a-card title="当前处理状态" class="info-card medical-theme-card" size="small">
          <a-descriptions :column="2" size="small" class="medical-descriptions">
            <a-descriptions-item label="处理状态">
              <a-tag :color="getStatusColor(eventInfo.status)" size="large">
                {{ getStatusText(eventInfo.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="处理阶段">
              <span class="process-stage">{{ getProcessStageText(eventInfo.status) }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="责任部门">
              <span class="responsible-dept">{{ eventInfo.responsibleDept || eventInfo.deptName || '-' }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="处理人员">
              <span class="handler">{{ eventInfo.currentHandler || '-' }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 审核历史 -->
        <a-card title="审核历史" class="info-card medical-theme-card" size="small">
          <div v-if="auditHistory && auditHistory.length > 0">
            <div v-for="(audit, index) in auditHistory" :key="index" class="audit-item">
              <div class="audit-header">
                <span class="audit-action">
                  <AuditOutlined />
                  {{ audit.action }}
                </span>
                <span class="audit-time">{{ formatDateTime(audit.time) }}</span>
              </div>
              <div class="audit-details">
                <p><strong>审核人：</strong>{{ audit.auditor }}</p>
                <div v-if="audit.comment" class="audit-comment">
                  {{ audit.comment }}
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <a-empty description="暂无审核历史" :image="false" />
          </div>
        </a-card>

        <!-- 相关附件 -->
        <a-card title="相关附件" class="info-card medical-theme-card" size="small">
          <div class="attachments-section">
            <div v-if="attachments && attachments.length > 0" class="attachments-list">
              <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
                <div class="attachment-info">
                  <FileOutlined class="attachment-icon" />
                  <div class="attachment-details">
                    <div class="attachment-name">{{ attachment.name }}</div>
                    <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                  </div>
                </div>
                <div class="attachment-actions">
                  <a-button size="small" @click="handlePreviewAttachment(attachment)">
                    预览
                  </a-button>
                  <a-button size="small" @click="handleDownloadAttachment(attachment)">
                    下载
                  </a-button>
                </div>
              </div>
            </div>
            <div v-else class="no-attachments">
              <a-empty description="暂无相关附件" :image="false" />
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  FileOutlined,
  EditOutlined,
  AuditOutlined,
  FileTextOutlined,
  MinusCircleOutlined
} from '@ant-design/icons-vue'
import type { EventInfo } from '@/types/moduleCommunication'
import { getEventInfo } from '@/api/evt/analyze'

// 定义动态字段接口
interface DynamicField {
  key: string
  label: string
  value: any
  type: string
  isTable: boolean
  isEmpty: boolean
  hasValue: boolean
}

// 定义Props
interface Props {
  eventId?: string
  eventInfo?: EventInfo | null
  loading?: boolean
  error?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  eventId: '',
  eventInfo: null,
  loading: false,
  error: null
})

// 响应式数据
const internalLoading = ref(false)
const internalError = ref<string | null>(null)
const loadedEventInfo = ref<EventInfo | null>(null)
const auditHistory = ref<any[]>([])
const attachments = ref<any[]>([])

// 动态字段相关
const showOnlyFilledFields = ref(false)
const dynamicFields = ref<DynamicField[]>([])

// 计算属性
const eventInfo = computed(() => props.eventInfo || loadedEventInfo.value)
const loading = computed(() => props.loading || internalLoading.value)
const error = computed(() => props.error || internalError.value)

// 过滤后的动态字段
const filteredDynamicFields = computed(() => {
  if (showOnlyFilledFields.value) {
    return dynamicFields.value.filter(field => field.hasValue)
  }
  return dynamicFields.value
})

// 已填写字段数量
const filledFieldsCount = computed(() => {
  return dynamicFields.value.filter(field => field.hasValue).length
})

// 空字段数量
const emptyFieldsCount = computed(() => {
  return dynamicFields.value.filter(field => field.isEmpty).length
})

// 基础方法
const formatDateTime = (dateTime: any) => {
  if (!dateTime) return '-'
  try {
    return new Date(dateTime).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return dateTime
  }
}

const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'orange',
    'processing': 'blue',
    'completed': 'green',
    'rejected': 'red',
    '0': 'orange',
    '1': 'blue',
    '2': 'green',
    '3': 'red'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'rejected': '已拒绝',
    '0': '待处理',
    '1': '处理中',
    '2': '已完成',
    '3': '已拒绝'
  }
  return statusMap[status] || '未知状态'
}

const getLevelColor = (level: string) => {
  if (!level) return '#d9d9d9'
  const levelMap: Record<string, string> = {
    'I': '#ff4d4f',
    'II': '#ff7a45',
    'III': '#ffa940',
    'IV': '#52c41a',
    'I级': '#ff4d4f',
    'II级': '#ff7a45',
    'III级': '#ffa940',
    'IV级': '#52c41a',
    '1': '#ff4d4f',
    '2': '#ff7a45',
    '3': '#ffa940',
    '4': '#52c41a'
  }
  return levelMap[level] || '#d9d9d9'
}

const getLevelText = (level: string) => {
  if (!level) return '-'
  if (level.includes('级')) return level
  if (['I', 'II', 'III', 'IV'].includes(level)) return level + '级'
  if (['1', '2', '3', '4'].includes(level)) {
    const romanMap: Record<string, string> = { '1': 'I', '2': 'II', '3': 'III', '4': 'IV' }
    return romanMap[level] + '级'
  }
  return level
}

const getCriticalColor = (critical: string) => {
  if (!critical) return '#d9d9d9'
  const criticalMap: Record<string, string> = {
    '无伤害': '#52c41a',
    '轻微伤害': '#faad14',
    '中度伤害': '#fa8c16',
    '重度伤害': '#f5222d',
    '死亡': '#722ed1'
  }
  return criticalMap[critical] || '#d9d9d9'
}

const getCriticalText = (critical: string) => {
  return critical || '-'
}

const getProcessStageText = (status: string) => {
  const stageMap: Record<string, string> = {
    'pending': '初始阶段',
    'processing': '分析阶段',
    'completed': '完成阶段',
    'rejected': '终止阶段',
    '0': '初始阶段',
    '1': '分析阶段',
    '2': '完成阶段',
    '3': '终止阶段'
  }
  return stageMap[status] || '未知阶段'
}

const getFieldDisplayValue = (field: DynamicField) => {
  if (field.isEmpty) {
    return '-'
  }
  return field.value || '-'
}

const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}

// 事件处理函数
const handleRetry = () => {
  loadEventInfo()
}

const handlePreviewAttachment = (attachment: any) => {
  console.log('预览附件:', attachment)
  message.info('预览功能开发中')
}

const handleDownloadAttachment = (attachment: any) => {
  console.log('下载附件:', attachment)
  message.info('下载功能开发中')
}

// 加载事件信息
const loadEventInfo = async () => {
  if (!props.eventId) {
    console.warn('EventInfoDisplay: eventId为空')
    return
  }

  internalLoading.value = true
  internalError.value = null

  try {
    console.log('🔍 [EventInfoDisplay] 开始加载事件信息:', props.eventId)

    // 使用API获取事件信息
    const response = await getEventInfo(props.eventId)

    if (response && response.id) {
      loadedEventInfo.value = response
      console.log('✅ [EventInfoDisplay] API响应:', response)

      // 处理动态字段
      if (response.dynamicFields && Array.isArray(response.dynamicFields)) {
        dynamicFields.value = response.dynamicFields.map((field: any) => ({
          key: field.key || field.fieldKey,
          label: field.label || field.fieldName,
          value: field.value || field.fieldValue || '',
          type: field.type || 'text',
          isTable: field.type === 'table',
          hasValue: !!(field.value || field.fieldValue),
          isEmpty: !(field.value || field.fieldValue)
        }))
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (err: any) {
    console.error('❌ [EventInfoDisplay] 加载失败:', err)
    internalError.value = err.message || '加载事件信息失败'
  } finally {
    internalLoading.value = false
  }
}

// 监听eventId变化
watch(() => props.eventId, (newEventId) => {
  if (newEventId) {
    loadEventInfo()
  }
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
  // 初始化默认动态字段
  dynamicFields.value = [
    { key: 'location', label: '发生地点类别', value: '高危服区域(手术、介入、分娩室与血液透析室等)', type: 'select', isTable: false, isEmpty: false, hasValue: true },
    { key: 'disease', label: '诊疗疾病类别', value: '损伤、中毒和外因的某些其他后果', type: 'select', isTable: false, isEmpty: false, hasValue: true },
    { key: 'severity', label: '事件严重程度', value: '', type: 'select', isTable: false, isEmpty: true, hasValue: false },
    { key: 'cause', label: '事件原因分析', value: '', type: 'textarea', isTable: false, isEmpty: true, hasValue: false },
    { key: 'prevention', label: '预防措施', value: '', type: 'textarea', isTable: false, isEmpty: true, hasValue: false },
    { key: 'witness', label: '目击人员', value: '', type: 'text', isTable: false, isEmpty: true, hasValue: false },
    { key: 'equipment', label: '相关设备', value: '', type: 'text', isTable: false, isEmpty: true, hasValue: false },
    { key: 'medicine', label: '相关药品', value: '', type: 'text', isTable: false, isEmpty: true, hasValue: false }
  ]
})
</script>

<style scoped lang="less">
.event-info-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .loading-container,
  .error-container,
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;

    .loading-text {
      margin-top: 16px;
      color: #666;
    }
  }

  .event-info-content {
    flex: 1;
    overflow: hidden;

    .scrollable-content {
      height: 100%;
      overflow-y: auto;
      padding: 16px;

      .info-card {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &.medical-theme-card {
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          border: 1px solid #e8f4fd;

          :deep(.ant-card-head) {
            background: linear-gradient(135deg, #e8f4fd 0%, #ffffff 100%);
            border-bottom: 1px solid #d1ecf1;
          }
        }

        .status-tag {
          font-weight: 500;
        }

        .medical-descriptions {
          :deep(.ant-descriptions-item-label) {
            font-weight: 500;
            color: #2c3e50;
          }

          .event-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #1890ff;
          }

          .event-name {
            font-weight: 500;
            color: #2c3e50;
          }

          .event-time {
            color: #666;
          }

          .event-dept {
            color: #52c41a;
            font-weight: 500;
          }
        }

        .event-summary-section,
        .additional-notes-section {
          h4 {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
          }

          .summary-content,
          .notes-content {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
            color: #666;
            line-height: 1.6;
          }
        }

        .card-title-with-controls {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        }

        .dynamic-fields-section {
          .dynamic-fields-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;

            .dynamic-field-item {
              padding: 12px;
              border: 1px solid #e8e8e8;
              border-radius: 6px;
              background: #ffffff;

              &.filled-field {
                border-color: #52c41a;
                background: #f6ffed;
              }

              &.empty-field {
                border-color: #d9d9d9;
                background: #fafafa;
              }

              .field-label {
                font-weight: 500;
                color: #2c3e50;
                margin-bottom: 8px;

                .empty-indicator {
                  color: #999;
                  font-size: 12px;
                }
              }

              .field-value {
                .text-field-value {
                  color: #666;

                  &.empty-value {
                    color: #999;
                    font-style: italic;
                  }
                }

                .table-field-container {
                  :deep(table) {
                    width: 100%;
                    border-collapse: collapse;

                    th, td {
                      border: 1px solid #e8e8e8;
                      padding: 8px;
                      text-align: left;
                    }

                    th {
                      background: #f5f5f5;
                      font-weight: 500;
                    }
                  }
                }
              }
            }
          }

          .no-dynamic-fields {
            text-align: center;
            padding: 40px;
            color: #999;
          }
        }

        .audit-item {
          padding: 12px;
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          margin-bottom: 12px;
          background: #ffffff;

          .audit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .audit-action {
              font-weight: 500;
              color: #1890ff;

              .anticon {
                margin-right: 4px;
              }
            }

            .audit-time {
              color: #999;
              font-size: 12px;
            }
          }

          .audit-details {
            color: #666;

            .audit-comment {
              margin-top: 8px;
              padding: 8px;
              background: #f8f9fa;
              border-radius: 4px;
              border-left: 3px solid #1890ff;
            }
          }
        }

        .attachments-section {
          .attachments-list {
            .attachment-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px;
              border: 1px solid #e8e8e8;
              border-radius: 6px;
              margin-bottom: 8px;
              background: #ffffff;

              .attachment-info {
                display: flex;
                align-items: center;

                .attachment-icon {
                  margin-right: 12px;
                  color: #1890ff;
                  font-size: 16px;
                }

                .attachment-details {
                  .attachment-name {
                    font-weight: 500;
                    color: #2c3e50;
                  }

                  .attachment-size {
                    color: #999;
                    font-size: 12px;
                  }
                }
              }

              .attachment-actions {
                display: flex;
                gap: 8px;
              }
            }
          }

          .no-attachments {
            text-align: center;
            padding: 40px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
